import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import {
  GET_ADMIN_MVT_WALLET_BALANCE,
  GET_USER_MVT_WALLET_BALANCE,
  GET_ORGANIZATION_MVT_WALLET_BALANCE,
  LIST_USER_MVT_BALANCES,
  LIST_ORGANIZATION_MVT_BALANCES,
  GET_MVT_WALLET_TRANSACTION_LIST,
  GET_ORGANIZATION_MVT_WALLET_TRANSACTION_LIST,
  GET_MVT_WALLET_TRANSACTION_BY_ID,
  ADMIN_MINT_MVT,
  ADMIN_TRANSFER_MVT,
  USER_TRANSFER_MVT,
  USER_TRANSFER_MVT_TO_ORGANIZATION,
  ADMIN_TRANSFER_MVT_TO_ORGANIZATION,
  ADMIN_DEPOSIT_USDC,
  ADMIN_WITHDRAW_USDC,
  GET_USDC_LIQUIDITY_POOL,
  GET_MVT_WALLET_EXCHANGE_RATE,
  GET_MVT_WALLET_SWAP_REQUESTS,
  APPROVE_MVT_WALLET_SWAP,
  REJECT_MVT_WALLET_SWAP,
  REQUEST_MVT_WALLET_SWAP,
  CREATE_ONRAMP_SESSION
} from '../graphql/mvt-wallet-graphql.queries';
import { BaseApiService } from '../../../shared/services/base-api.service';

@Injectable({
  providedIn: 'root'
})
export class MvtWalletService {
  constructor(
    private readonly baseApiService: BaseApiService,
    private readonly apollo: Apollo
  ) { }

  /**
   * List all user MVT wallet balances
   * @param limit Optional limit for pagination
   * @param nextToken Optional token for pagination
   * @returns Observable with list of user balances
   */
  listUserMVTBalances(limit: number = 1000, nextToken?: string): Observable<any> {

    return this.baseApiService.customQuery(
      LIST_USER_MVT_BALANCES,
      { limit, nextToken }
    ).pipe(
      tap({
        next: (response) => {
        },
        error: (error) => {
          console.error('Error details:', {
            message: error.message,
            graphQLErrors: error.graphQLErrors,
            networkError: error.networkError,
            extraInfo: error.extraInfo
          });
        }
      }),
      map(response => {
        if (!response || !response.data) {
          console.warn('No data in listUserMVTBalances response');
          return { items: [], nextToken: null };
        }

        const result = response.data.listUserMVTBalances;

        if (!result) {
          console.warn('listUserMVTBalances field is missing in response');
          return { items: [], nextToken: null };
        }

        return {
          items: result.items || [],
          nextToken: result.nextToken || null
        };
      }),
      catchError(error => {
        console.error('Error in listUserMVTBalances:', {
          error,
          message: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        });
        return of({ items: [], nextToken: null });
      })
    );
  }

  /**
   * List all organization MVT wallet balances
   * @param limit Optional limit for pagination
   * @param nextToken Optional token for pagination
   * @returns Observable with list of organization balances
   */
  listOrganizationMVTBalances(limit: number = 1000, nextToken?: string): Observable<any> {
    return this.baseApiService.customQuery(
      LIST_ORGANIZATION_MVT_BALANCES,
      { limit, nextToken }
    ).pipe(
      tap({
        next: (response) => {
        },
        error: (error) => {
          console.error('Error details:', {
            message: error.message,
            graphQLErrors: error.graphQLErrors,
            networkError: error.networkError,
            extraInfo: error.extraInfo
          });
        }
      }),
      map(response => {
        if (!response || !response.data) {
          console.warn('No data in listOrganizationMVTBalances response');
          return { items: [], nextToken: null };
        }

        const result = response.data.listOrganizationMVTBalances;

        if (!result) {
          console.warn('listOrganizationMVTBalances field is missing in response');
          return { items: [], nextToken: null };
        }

        return {
          items: result.items || [],
          nextToken: result.nextToken || null
        };
      }),
      catchError(error => {
        console.error('Error in listOrganizationMVTBalances:', {
          error,
          message: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        });
        return of({ items: [], nextToken: null });
      })
    );
  }

  /**
   * Get MVT wallet balance for a user by user ID
   * @param input Object containing userId
   * @returns Observable with balance data
   */
  getUserMVTWalletBalance(input: { userId: string }): Observable<any> {
    if (!input.userId) {
      return throwError(() => new Error('User ID is required'));
    }

    return this.baseApiService.customQuery(
      GET_USER_MVT_WALLET_BALANCE,
      { input }
    ).pipe(
      catchError(error => {
        console.error('Error fetching user MVT wallet balance:', error);
        return throwError(() => new Error('Failed to fetch user MVT wallet balance'));
      })
    );
  }

  /**
   * Get MVT wallet balance for an organization by member ID
   * @param input Object containing memberId
   * @returns Observable with balance data
   */
  getOrganizationMVTWalletBalance(input: { memberId: string }): Observable<any> {
    if (!input.memberId) {
      return throwError(() => new Error('Member ID is required'));
    }

    return this.baseApiService.customQuery(
      GET_ORGANIZATION_MVT_WALLET_BALANCE,
      { input: { organizationId: input.memberId } }
    ).pipe(
      map((response: any) => {
        if (response?.data?.getOrganizationMVTWalletBalance?.data) {
          return response;
        }
        throw new Error('Invalid response format from server');
      }),
      catchError(error => {
        console.error('Error fetching organization MVT wallet balance:', error);
        return throwError(() => new Error('Failed to fetch organization MVT wallet balance'));
      })
    );
  }

  /**
   * Get admin MVT wallet balance with enhanced data (totalMinted, totalTransferred)
   * @returns Observable with enhanced admin wallet data
   */
  getAdminMVTWalletBalance(): Observable<any> {
    return this.baseApiService.customQuery(
      GET_ADMIN_MVT_WALLET_BALANCE,
      {}
    ).pipe(
      catchError(error => {
        console.error('Error fetching admin MVT wallet balance:', error);
        return throwError(() => new Error('Failed to fetch admin MVT wallet balance'));
      })
    );
  }

  /**
   * Get USDC liquidity pool data
   * @returns Observable with USDC liquidity pool data
   */
  getUSDCLiquidityPool(): Observable<any> {

    return this.baseApiService.customQuery(
      GET_USDC_LIQUIDITY_POOL,
      {}
    ).pipe(
      map((result: any) => {

        const response = result.data.getUSDCLiquidityPool;

        if (response.statusCode !== 200) {
          console.error('Backend returned error:', response.message);
          throw new Error(response.message ?? 'Failed to fetch USDC liquidity pool data');
        }

        return {
          data: {
            getUSDCLiquidityPool: response
          }
        };
      }),
      catchError(error => {
        console.error('Error fetching USDC liquidity pool data:', error);
        return throwError(() => new Error('Failed to fetch USDC liquidity pool data'));
      })
    );
  }

  /**
   * Get MVT balance for a given address
   * @param address Wallet address
   * @param isAdmin Whether the request is from admin
   * @returns Observable with balance data
   */
  getMVTBalance(address: string, isAdmin: boolean): Observable<any> {
    if (!address) {
      return throwError(() => new Error('Wallet address is required'));
    }

    if (isAdmin) {
      return this.baseApiService.customQuery(
        GET_ADMIN_MVT_WALLET_BALANCE,
        {}
      ).pipe(
        map((result: any) => ({
          data: {
            getMVTBalance: {
              data: {
                id: result.data.getAdminMVTWalletBalance.data.walletAddress,
                balance: result.data.getAdminMVTWalletBalance.data.balance,
                contractAddress: result.data.getAdminMVTWalletBalance.data.contractAddress
              }
            }
          }
        })),
        catchError(error => {
          console.error('Error fetching admin MVT balance:', error);
          return throwError(() => new Error('Failed to fetch admin MVT balance'));
        })
      );
    } else {
      return this.baseApiService.customQuery(
        GET_USER_MVT_WALLET_BALANCE,
        { input: { walletAddress: address } }
      ).pipe(
        map((result: any) => ({
          data: {
            getMVTBalance: {
              data: {
                id: result.data.getUserMVTWalletBalance.data.walletAddress,
                balance: result.data.getUserMVTWalletBalance.data.balance,
                userId: result.data.getUserMVTWalletBalance.data.userId
              }
            }
          }
        })),
        catchError(error => {
          console.error('Error fetching user MVT balance:', error);
          return throwError(() => new Error('Failed to fetch user MVT balance'));
        })
      );
    }
  }

  /**
   * Get MVT transaction list for a wallet
   * @param address Wallet address
   * @param isAdmin Whether the request is from admin
   * @param limit Number of transactions to return (optional)
   * @returns Observable with transaction list
   */
  getMVTTransactionList(address: string, isAdmin: boolean, limit: number = 10): Observable<any> {
    if (!address) {
      return throwError(() => new Error('Wallet address is required'));
    }

    return this.baseApiService.customQuery(
      GET_MVT_WALLET_TRANSACTION_LIST,
      { address, isAdmin, limit }
    ).pipe(
      map((result: any) => {

        const response = result.data.getMVTWalletTransactionList;

        if (response.statusCode !== 200) {
          console.error('Backend returned error:', response.message);
          throw new Error(response.message ?? 'Failed to fetch transactions');
        }

        const mappedData = (response.data ?? []).map((tx: any) => ({
          id: tx.id,
          transactionType: tx.transactionType,
          tokenType: tx.tokenType || 'MVT',
          amount: tx.amount,
          fromWalletId: tx.fromWalletId,
          toWalletId: tx.toWalletId,
          fromUserId: tx.fromUserId,
          fromUser: tx.fromUser,
          toUserId: tx.toUserId,
          toUser: tx.toUser,
          toOrganizationId: tx.toOrganizationId,
          toOrganization: tx.toOrganization,
          status: tx.status,
          transactionHash: tx.transactionHash,
          internalTxId: tx.internalTxId,
          description: tx.description,
          adminUserId: tx.adminUserId,
          gasUsed: tx.gasUsed,
          gasPrice: tx.gasPrice,
          blockNumber: tx.blockNumber,
          confirmations: tx.confirmations,
          metadata: tx.metadata,
          createdAt: tx.createdAt,
          updatedAt: tx.updatedAt,
          displayType: tx.displayType,
          primaryLabel: tx.primaryLabel,
          secondaryInfo: tx.secondaryInfo,
          showEtherscanLink: tx.showEtherscanLink,
          formattedDate: tx.formattedDate,
          type: tx.transactionType,
          from: tx.fromUserId ?? tx.fromWalletId,
          to: tx.toUserId ?? tx.toWalletId,
          timestamp: tx.createdAt,
          date: tx.createdAt
        }));

        return {
          data: {
            getMVTTransactionList: {
              data: mappedData
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error details:', JSON.stringify(error, null, 2));
        return throwError(() => new Error('Failed to fetch MVT transactions'));
      })
    );
  }

  /**
   * Get MVT transaction list for an organization
   * @param organizationId Organization ID to filter transactions
   * @param limit Number of transactions to return (optional)
   * @returns Observable with organization transaction list
   */
  getOrganizationMVTTransactionList(organizationId: string, limit: number = 50): Observable<any> {
    if (!organizationId) {
      return throwError(() => new Error('Organization ID is required'));
    }

    return this.baseApiService.customQuery(
      GET_ORGANIZATION_MVT_WALLET_TRANSACTION_LIST,
      { organizationId, limit }
    ).pipe(
      map((result: any) => {
        if (result.errors && result.errors.length > 0) {
          console.error('GraphQL errors fetching organization MVT transactions:', result.errors);
          const errorMessage = result.errors.map(e => e.message).join(', ');
          throw new Error(`Failed to fetch organization MVT transactions: ${errorMessage}`);
        }

        if (!result.data || !result.data.getOrganizationMVTWalletTransactionList) {
          console.error('Invalid response structure for getOrganizationMVTTransactionList', result);
          throw new Error('Failed to fetch organization MVT transactions due to an invalid response from the server.');
        }
        const response = result.data.getOrganizationMVTWalletTransactionList;

        if (response.statusCode !== 200) {
          console.error('Backend returned error:', response.message);
          throw new Error(response.message ?? 'Failed to fetch organization transactions');
        }

        const mappedData = (response.data ?? []).map((tx: any) => ({
          id: tx.id,
          transactionType: tx.transactionType,
          tokenType: tx.tokenType || 'MVT',
          amount: tx.amount,
          fromWalletId: tx.fromWalletId,
          toWalletId: tx.toWalletId,
          fromUserId: tx.fromUserId,
          fromUser: tx.fromUser,
          toUserId: tx.toUserId,
          toUser: tx.toUser,
          organizationId: tx.organizationId,
          memberId: tx.memberId,
          status: tx.status,
          transactionHash: tx.transactionHash,
          internalTxId: tx.internalTxId,
          description: tx.description,
          adminUserId: tx.adminUserId,
          gasUsed: tx.gasUsed,
          gasPrice: tx.gasPrice,
          blockNumber: tx.blockNumber,
          confirmations: tx.confirmations,
          metadata: tx.metadata,
          createdAt: tx.createdAt,
          updatedAt: tx.updatedAt,
          displayType: tx.displayType,
          primaryLabel: tx.primaryLabel,
          secondaryInfo: tx.secondaryInfo,
          showEtherscanLink: tx.showEtherscanLink,
          formattedDate: tx.formattedDate,
          type: tx.transactionType,
          from: tx.fromUserId ?? tx.fromWalletId,
          to: tx.toUserId ?? tx.toWalletId,
          timestamp: tx.createdAt,
          date: tx.createdAt
        }));

        return {
          data: {
            getOrganizationMVTTransactionList: {
              data: mappedData
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error fetching organization MVT transactions:', error);
        return throwError(() => new Error('Failed to fetch organization MVT transactions'));
      })
    );
  }

  /**
   * Get MVT transaction by ID
   * @param transactionId Transaction ID
   * @returns Observable with transaction details
   */
  getMVTTransactionById(transactionId: string): Observable<any> {
    if (!transactionId) {
      return throwError(() => new Error('Transaction ID is required'));
    }

    return this.baseApiService.customQuery(
      GET_MVT_WALLET_TRANSACTION_BY_ID,
      { id: transactionId }
    ).pipe(
      map((result: any) => {
        const response = result.data.getMVTWalletTransactionById;

        if (response.statusCode !== 200) {
          throw new Error(response.message ?? 'Failed to fetch transaction');
        }

        const tx = response.data;
        return {
          data: {
            getMVTTransactionById: {
              data: {
                id: tx.id,
                transactionType: tx.transactionType,
                amount: tx.amount,
                fromWalletId: tx.fromWalletId,
                toWalletId: tx.toWalletId,
                fromUserId: tx.fromUserId,
                toUserId: tx.toUserId,
                status: tx.status,
                transactionHash: tx.transactionHash,
                internalTxId: tx.internalTxId,
                description: tx.description,
                adminUserId: tx.adminUserId,
                gasUsed: tx.gasUsed,
                gasPrice: tx.gasPrice,
                blockNumber: tx.blockNumber,
                confirmations: tx.confirmations,
                metadata: tx.metadata,
                createdAt: tx.createdAt,
                updatedAt: tx.updatedAt,
                type: tx.transactionType,
                from: tx.fromUserId ?? tx.fromWalletId,
                to: tx.toUserId ?? tx.toWalletId,
                timestamp: tx.createdAt,
                date: tx.createdAt,
                tokenType: 'MVT'
              }
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error fetching MVT transaction:', error);
        return throwError(() => new Error('Failed to fetch MVT transaction'));
      })
    );
  }

  /**
   * Admin: Mint MVT tokens
   * @param amount Amount to mint
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  adminMintMVT(amount: number, description: string = 'Minting MVT tokens'): Observable<any> {
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      ADMIN_MINT_MVT,
      { amount, description }
    ).pipe(
      map((result: any) => ({
        data: { adminMintMVT: result.data.adminMintMVT }
      })),
      catchError(error => {
        console.error('Error minting MVT:', error);
        return throwError(() => new Error('Failed to mint MVT tokens'));
      })
    );
  }

  /**
   * Admin: Transfer MVT tokens to a user
   * @param userId Recipient user ID
   * @param amount Amount to transfer
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  adminTransferMVT(userId: string, amount: number, description: string = 'MVT token transfer'): Observable<any> {
    if (!userId) {
      return throwError(() => new Error('User ID is required'));
    }
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      ADMIN_TRANSFER_MVT,
      { userId, amount, description }
    ).pipe(
      map((result: any) => ({
        data: { adminTransferMVT: result.data.adminTransferMVT }
      })),
      catchError(error => {
        console.error('Error transferring MVT:', error);
        return throwError(() => new Error('Failed to transfer MVT tokens'));
      })
    );
  }

  /**
   * User: Transfer MVT tokens to another user
   * @param recipientUserId Recipient user ID
   * @param amount Amount to transfer
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  userTransferMVT(recipientUserId: string, amount: number, description: string = 'MVT token transfer'): Observable<any> {
    if (!recipientUserId) {
      return throwError(() => new Error('Recipient user ID is required'));
    }
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      USER_TRANSFER_MVT,
      { recipientUserId, amount, description }
    ).pipe(
      map((result: any) => ({
        data: { userTransferMVT: result.data.userTransferMVT }
      })),
      catchError(error => {
        console.error('Error transferring MVT:', error);
        return throwError(() => new Error('Failed to transfer MVT tokens'));
      })
    );
  }

  /**
   * User: Transfer MVT tokens to an organization
   * @param memberId Member ID of the organization
   * @param amount Amount to transfer
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  userTransferMVTToOrganization(memberId: string, amount: number, description: string = 'MVT token transfer to organization'): Observable<any> {
    if (!memberId) {
      return throwError(() => new Error('Member ID is required'));
    }
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      USER_TRANSFER_MVT_TO_ORGANIZATION,
      { memberId, amount, description }
    ).pipe(
      map((result: any) => ({
        data: { userTransferMVTToOrganization: result.data.userTransferMVTToOrganization }
      })),
      catchError(error => {
        console.error('Error transferring MVT to organization:', error);
        return throwError(() => new Error('Failed to transfer MVT tokens to organization'));
      })
    );
  }

  /**
   * Admin: Transfer MVT tokens to organization (for activity rewards)
   * @param memberId Member ID of the organization
   * @param amount Amount to transfer
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  adminTransferMVTToOrganization(memberId: string, amount: number, description: string = 'Admin MVT token transfer to organization for activity reward'): Observable<any> {
    if (!memberId) {
      return throwError(() => new Error('Member ID is required'));
    }
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      ADMIN_TRANSFER_MVT_TO_ORGANIZATION,
      { memberId, amount, description }
    ).pipe(
      map((result: any) => ({
        data: { adminTransferMVTToOrganization: result.data.adminTransferMVTToOrganization }
      })),
      catchError(error => {
        console.error('Error transferring MVT to organization:', error);
        return throwError(() => new Error('Failed to transfer MVT tokens to organization'));
      })
    );
  }

  /**
   * Admin: Deposit USDC to the liquidity pool
   * @param amount Amount to deposit
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  adminDepositUSDC(amount: number, description: string = 'Admin USDC deposit'): Observable<any> {
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      ADMIN_DEPOSIT_USDC,
      { amount, description }
    ).pipe(
      map((result: any) => ({
        data: { depositUSDC: result.data.adminDepositUSDC }
      })),
      catchError(error => {
        console.error('Error depositing USDC:', error);
        return throwError(() => new Error('Failed to deposit USDC'));
      })
    );
  }

  /**
   * Admin: Withdraw USDC from the liquidity pool
   * @param amount Amount to withdraw
   * @param description Transaction description (optional)
   * @returns Observable with transaction result
   */
  adminWithdrawUSDC(amount: number, description: string = 'Admin USDC withdrawal'): Observable<any> {
    if (!amount || amount <= 0) {
      return throwError(() => new Error('Invalid amount'));
    }

    return this.baseApiService.mutateData(
      ADMIN_WITHDRAW_USDC,
      { amount, description }
    ).pipe(
      map((result: any) => ({
        data: { withdrawUSDC: result.data.adminWithdrawUSDC }
      })),
      catchError(error => {
        console.error('Error withdrawing USDC:', error);
        return throwError(() => new Error('Failed to withdraw USDC'));
      })
    );
  }

  /**
   * Get MVT wallet exchange rate
   * @returns Observable with exchange rate data
   */
  getMVTWalletExchangeRate(): Observable<any> {

    return this.baseApiService.customQuery(
      GET_MVT_WALLET_EXCHANGE_RATE,
      {}
    ).pipe(
      map((result: any) => {

        const response = result.data.getMVTWalletExchangeRate;

        if (response.statusCode !== 200) {
          console.error('Backend returned error:', response.message);
          throw new Error(response.message ?? 'Failed to fetch exchange rate');
        }

        return {
          data: {
            getMVTWalletExchangeRate: response
          }
        };
      }),
      catchError(error => {
        console.error('Error fetching MVT wallet exchange rate:', error);
        return throwError(() => new Error('Failed to fetch MVT wallet exchange rate'));
      })
    );
  }

  /**
   * Request MVT wallet swap (user operation)
   * @param mvtAmount Amount of MVT to swap
   * @param description Optional description for the swap request
   * @returns Observable with swap request result
   */
  requestMVTWalletSwap(mvtAmount: number, description?: string): Observable<any> {
    if (!mvtAmount || mvtAmount <= 0) {
      return throwError(() => new Error('Invalid MVT amount'));
    }

    return this.baseApiService.mutateData(
      REQUEST_MVT_WALLET_SWAP,
      { mvtAmount, description }
    ).pipe(
      map((result: any) => {

        const response = result.data.requestMVTWalletSwap;

        return {
          data: {
            requestMVTSwap: {
              statusCode: response.statusCode,
              message: response.message,
              data: {
                id: response.data?.id,
                mvtAmount: response.data?.mvtAmount,
                estimatedUsdcAmount: response.data?.usdcAmount,
                status: response.data?.status,
                date: response.data?.requestedAt ?? new Date().toISOString()
              }
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error requesting MVT wallet swap:', error);
        return throwError(() => new Error('Failed to request MVT wallet swap'));
      })
    );
  }

  /**
   * Get MVT wallet swap requests (admin view)
   * @param isAdmin Whether to get admin view (default: true for admin panel)
   * @param limit Number of requests to return (optional)
   * @param excludeFailed Whether to exclude failed transactions (default: true)
   * @returns Observable with swap requests data
   */
  getMVTWalletSwapRequests(isAdmin: boolean = true, limit?: number, excludeFailed: boolean = true): Observable<any> {

    return this.baseApiService.customQuery(
      GET_MVT_WALLET_SWAP_REQUESTS,
      { isAdmin, limit, excludeFailed }
    ).pipe(
      map((result: any) => {

        const response = result.data.getMVTWalletSwapRequests;

        if (response.statusCode !== 200) {
          console.error('Backend returned error:', response.message);
          throw new Error(response.message ?? 'Failed to fetch swap requests');
        }

        const mappedData = (response.data ?? []).map((request: any) => ({
          id: request.id,
          date: new Date(request.requestedAt),
          userId: request.userId,
          userName: request.userName ?? `User ${request.userId?.slice(-8) ?? 'Unknown'}`, // Fallback when userName not available
          walletAddress: request.userWalletAddress ?? '',
          mvtAmount: request.mvtAmount,
          usdcAmount: request.usdcAmount,
          status: request.status,
          transactionHash: request.transactionHash ?? null,
          requestedAt: request.requestedAt,
          processedAt: request.processedAt,
          description: request.description,
          exchangeRate: request.exchangeRate,
          expiresAt: request.expiresAt,
          adminUserId: request.adminUserId,
          rejectionReason: request.rejectionReason
        }));

        return {
          data: {
            getMVTSwapRequests: {
              statusCode: 200,
              message: response.message,
              data: mappedData
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error fetching MVT wallet swap requests:', error);
        return throwError(() => new Error('Failed to fetch MVT wallet swap requests'));
      })
    );
  }

  /**
   * Approve MVT wallet swap request (admin only)
   * @param swapRequestId Swap request ID to approve
   * @returns Observable with approval result
   */
  approveMVTWalletSwap(swapRequestId: string): Observable<any> {
    if (!swapRequestId) {
      return throwError(() => new Error('Swap request ID is required'));
    }

    return this.baseApiService.mutateData(
      APPROVE_MVT_WALLET_SWAP,
      { swapRequestId }
    ).pipe(
      map((result: any) => {

        const response = result.data.approveMVTWalletSwap;

        if (!response) {
          console.error('No response data received from approveMVTWalletSwap');
          throw new Error('No response data received from approval service');
        }

        if (response.statusCode !== 200) {
          console.error('Backend returned error status:', response.statusCode, response.message);
          throw new Error(response.message ?? `Backend error: ${response.statusCode}`);
        }

        if (!response.data) {
          console.error('No data in approval response');
          throw new Error('No data received from approval service');
        }

        const mappedResponse = {
          data: {
            approveMVTSwap: {
              statusCode: response.statusCode,
              message: response.message,
              data: {
                success: response.data?.success ?? true,
                fail: !(response.data?.success ?? true),
                txHash: response.data?.transactionHash,
                swapRequestId: response.data?.swapRequestId,
                processedAt: response.data?.processedAt,
                status: response.data?.status,
                mvtAmount: response.data?.mvtAmount,
                usdcAmount: response.data?.usdcAmount,
                newUserBalance: response.data?.newUserBalance
              }
            }
          }
        };

        if (!mappedResponse.data.approveMVTSwap.data.success) {
          console.error('Approval marked as failed in response data');
          throw new Error('Approval operation failed according to response data');
        }

        return mappedResponse;
      }),
      catchError(error => {
        console.error('Error approving MVT wallet swap:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));

        let errorMessage = 'Failed to approve MVT wallet swap';

        if (error?.message) {
          errorMessage = error.message;
        } else if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.graphQLErrors?.length > 0) {
          errorMessage = error.graphQLErrors[0].message;
        } else if (error?.networkError?.message) {
          errorMessage = `Network error: ${error.networkError.message}`;
        }

        if (errorMessage.includes('status update') || errorMessage.includes('Failed to update')) {
          errorMessage = 'Failed to update swap request status. Please try again or contact support.';
        } else if (errorMessage.includes('not found')) {
          errorMessage = 'Swap request not found. It may have been processed already.';
        } else if (errorMessage.includes('Unauthorized') || errorMessage.includes('forbidden')) {
          errorMessage = 'You do not have permission to approve swap requests.';
        }

        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Reject MVT wallet swap request (admin only)
   * @param swapRequestId Swap request ID to reject
   * @param rejectionReason Reason for rejection (optional)
   * @returns Observable with rejection result
   */
  rejectMVTWalletSwap(swapRequestId: string, rejectionReason?: string): Observable<any> {
    if (!swapRequestId) {
      return throwError(() => new Error('Swap request ID is required'));
    }


    return this.baseApiService.mutateData(
      REJECT_MVT_WALLET_SWAP,
      {
        swapRequestId,
        rejectionReason: rejectionReason || 'Request rejected by admin'
      }
    ).pipe(
      map((response: any) => {
        return {
          data: {
            rejectMVTSwap: {
              statusCode: response.statusCode,
              message: response.message,
              data: {
                success: response.data?.success ?? (response.statusCode === 200),
                fail: !(response.data?.success ?? (response.statusCode === 200)),
                swapRequestId: response.data?.swapRequestId,
                rejectionReason: response.data?.rejectionReason,
                processedAt: response.data?.processedAt
              }
            }
          }
        };
      }),
      catchError(error => {
        console.error('Error rejecting MVT wallet swap:', error);
        return throwError(() => new Error('Failed to reject MVT wallet swap'));
      })
    );
  }

  /**
   * Create Stripe onramp session for MVT wallet purchase
   * @param usdcAmount Amount of USDC to purchase
   * @param userId User ID for MVT wallet transfer
   * @param mvtAmount Amount of MVT tokens to transfer after purchase
   * @param exchangeRate Exchange rate used for conversion
   * @returns Observable with session information
   */
  createOnrampSession(usdcAmount: string, userId: string, mvtAmount?: string, exchangeRate?: string): Observable<any> {
    if (!usdcAmount || !userId) {
      return throwError(() => new Error('USDC amount and user ID are required'));
    }

    return this.apollo.mutate({
      mutation: CREATE_ONRAMP_SESSION,
      variables: {
        usdcAmount,
        userWallet: userId,
        mvtAmount,
        exchangeRate
      }
    }).pipe(
      map((result: any) => {
        return result;
      }),
      catchError(error => {
        console.error('Error creating onramp session:', error);
        return throwError(() => new Error('Failed to create onramp session'));
      })
    );
  }
}
