import { Component, <PERSON><PERSON><PERSON>roy, OnInit, Input, ViewChild } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';

import { MvtWalletService } from '../../../../pages/customers/services/mvt-wallet.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { TasksService } from '../../services/tasks.service';

@Component({
  selector: 'app-funding-tokens',
  templateUrl: './funding-tokens.component.html',
  styleUrls: ['./funding-tokens.component.scss'],
})
export class FundingTokensComponent implements OnInit, OnDestroy {
  @Input() organizationId: string = '';

  @ViewChild('kt_datatable_example_1', { static: true }) kt_datatable_example_1: any;

  tasksList: any[] = [];
  invoicesList: any[] = [];
  pointsData: any = {};
  loading = {
    tasks: false,
    invoices: false,
    points: false,
    transactions: false
  };

  allStripeSubscriptions: any[] = [];
  userList: any = {};
  transactionList: any = [];
  latestActivities: any = [];
  noActivities: boolean;
  isMemberPage: boolean;
  allPointLogs: any = [];
  categoryWisePoints: any = {};

  constructor(
    private readonly tasksService: TasksService,
    private readonly sharedService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly spinner: NgxSpinnerService,
    private readonly mvtWalletService: MvtWalletService
  ) { }

  private subscriptions: any[] = [];

  /**
   * Clean up resources when the component is destroyed
   */
  ngOnDestroy(): void {
    this.spinner.hide('task_widget_spinner');

    this.subscriptions.forEach(sub => {
      if (sub && typeof sub.unsubscribe === 'function') {
        sub.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  ngOnInit(): void {
    this.spinner.show('task_widget_spinner');
    this.isMemberPage = this.activatedRoute.snapshot.url.join('/').split('/')[0]?.includes('member') || false;

    this.loadMVTTransactions();

    this.tasksList = [];
    this.invoicesList = [];

    this.getTasksList();
    this.getPointsData();
    this.getAllInvoices();

    this.sharedService.defaultCityId.subscribe((data: any) => {
      if (data) {
        this.getTasksList();
      }
    });

    this.sharedService.getUserListStripe().subscribe({
      next: (response: any) => {
        let users = response?.data?.userByDate?.items;
        users.forEach((user: any) => {
          if (user?.customerId != null) {
            this.userList[user?.customerId] = user;
          }
        });
        this.getAllInvoices();
      }
    });
  }

  /**
   * Transform raw transaction data into the format expected by the template
   */
  private transformTransactionData(transactions: any[]): any[] {
    if (!transactions || !Array.isArray(transactions)) {
      return [];
    }

    return transactions.map(transaction => ({
      id: transaction.transactionId || '',
      status: this.getTransactionStatus(transaction.status),
      date: transaction.timestamp ? new Date(transaction.timestamp).toLocaleDateString() : 'N/A',
      amount: transaction.amount || 0,
      type: this.getTransactionType(transaction.type),
      description: transaction.description || 'MVT Transaction',
      icon: this.getTransactionIcon(transaction.type)
    }));
  }

  /**
   * Get transaction status display text
   */
  private getTransactionStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'COMPLETED': 'Completed',
      'PENDING': 'Pending',
      'FAILED': 'Failed'
    };
    return statusMap[status] || 'Unknown';
  }

  /**
   * Get transaction type display text
   */
  private getTransactionType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'TRANSFER': 'Transfer',
      'REWARD': 'Reward',
      'PURCHASE': 'Purchase'
    };
    return typeMap[type] || 'Transaction';
  }

  /**
   * Get icon class based on transaction type
   */
  private getTransactionIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'TRANSFER': 'flaticon2-arrow',
      'REWARD': 'flaticon2-medal',
      'PURCHASE': 'flaticon2-shopping-cart'
    };
    return iconMap[type] || 'flaticon2-file';
  }

  /**
   * Load recent MVT transactions for the current user
   */
  private loadMVTTransactions(): void {
    this.loading.transactions = true;
    this.spinner.show('task_widget_spinner');

    const currentUser = this.sharedService.currentUser?.value || {};
    const walletAddress = currentUser?.walletAddress ? String(currentUser.walletAddress) : '';

    if (!walletAddress) {
      console.warn('No wallet address found for the current user');
      this.noActivities = true;
      this.loading.transactions = false;
      this.spinner.hide('task_widget_spinner');
      return;
    }

    const subscription = this.mvtWalletService.getMVTTransactionList(walletAddress, true, 5)
      .subscribe({
        next: (response: any) => {
          try {
            if (response?.data?.getMVTWalletTransactionList?.items) {
              const transactions = response.data.getMVTWalletTransactionList.items;
              const activities = this.transformTransactionData(transactions);

              activities.sort((a: any, b: any) =>
                new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
              );

              this.latestActivities = activities;
              this.noActivities = activities.length === 0;
            } else {
              console.warn('No transaction data found in the response');
              this.noActivities = true;
            }
          } catch (error) {
            console.error('Error processing transaction data:', error);
            this.noActivities = true;
          }
        },
        error: (error: any) => {
          console.error('Error loading MVT transactions:', error);
          this.noActivities = true;
          this.loading.transactions = false;
          this.spinner.hide('task_widget_spinner');
        },
        complete: () => {
          this.loading.transactions = false;
          this.spinner.hide('task_widget_spinner');
        }
      });
  }

  getTasksList() {
    this.tasksService.getTaskList(this.organizationId).subscribe({
      next: ({ data }: any) => {
        if (this.organizationId) {
          this.tasksList = data?.listTaskOrganizations?.items
            .filter(
              (element: any) =>
                element &&
                !element._deleted &&
                element?.task?.cityId === this.sharedService?.defaultCityId?.value
            )
            .map((element: any) => element.task);
        } else {
          this.tasksList = data.tasksByDate.items.filter(
            (element: any) =>
              element &&
              (!element._deleted && element?.status !== "Done") &&
              element?.cityId === this.sharedService?.defaultCityId?.value
          );
        }

        this.tasksList?.sort((element1: any, element2: any) => {
          return (
            new Date(element2.updatedAt)?.getTime() -
            new Date(element1.updatedAt)?.getTime()
          );
        });

        this.tasksList = this.tasksList?.slice(0, 7);
      },
      error: (error: any) => {
      },
    });
  }

  getAllInvoices(invoiceId?: any) {
    const cityId = this.sharedService.defaultCityId?.value || '';
    this.loading.invoices = true;

    const sub = this.sharedService.getCityFundTransactions(cityId).subscribe({
      next: (result: any) => {
        this.allStripeSubscriptions = (result?.data?.cityFundTransactionsByDate?.items || [])
          .filter((transaction: any) => transaction?.type !== 'tokenTransfer')
          .map((transaction: any) => ({
            ...transaction,
            createdAt: new Date(transaction.createdAt),
            name: transaction.type === 'subscription'
              ? (this.userList[transaction.name]?.name ?? transaction.name)
              : transaction.name
          }))
          .sort((a: any, b: any) => b.createdAt.getTime() - a.createdAt.getTime());

        this.loading.invoices = false;
        if (this.isMemberPage) {
          const memberIdParam = this.activatedRoute.snapshot.paramMap.get('id');
          if (memberIdParam) {
            const memberId = this.sharedService.getDecryptedId(memberIdParam);
            this.allStripeSubscriptions = this.allStripeSubscriptions.filter(
              (sub: any) => sub.memberId === memberId
            );
          }
        }
        this.transactionList = [...this.allStripeSubscriptions]
          .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 7);
        this.getPointsData();
      }
    })

  }

  calculateDiff(dueDatetr: string) {
    const date1: any = new Date(dueDatetr);
    const date2: any = new Date();
    const diffDays: any = Math.floor((date1 - date2) / (1000 * 60 * 60 * 24));

    return diffDays;
  }

  getPointsData() {
    console.log('Fetching all stakeholder MVT transactions and funding details...');
    this.spinner.show('task_widget_spinner');

    const mvtTransactions$ = this.mvtWalletService.getMVTTransactionList('all', true, 14);
    const fundingDetails$ = this.sharedService.getCityFundTransactions(this.sharedService.defaultCityId?.value);

    forkJoin([mvtTransactions$, fundingDetails$])
      .pipe(
        catchError(error => {
          console.error('Error in combined request:', error);
          return of([null, null]);
        })
      )
      .subscribe({
        next: ([mvtResponse, fundingResponse]) => {
          const allActivities = [
            ...this.extractMvtActivities(mvtResponse),
            ...this.extractFundingActivities(fundingResponse)
          ];

          this.latestActivities = allActivities
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 7);

          this.noActivities = this.latestActivities.length === 0;
          this.spinner.hide('task_widget_spinner');
        },
        error: error => {
          console.error('Error loading combined transactions:', error);
          this.noActivities = true;
          this.spinner.hide('task_widget_spinner');
        }
      });
  }

  private extractMvtActivities(response: any): any[] {
    const transactions = response?.data?.getMVTTransactionList?.data || [];
    return transactions
      .filter(tx => tx?.amount > 0 && (tx.tokenType || 'MVT').toUpperCase() === 'MVT' && tx.displayType === 'SENT')
      .filter(tx => tx.toUser || tx.toOrganizationId || tx.metadata?.includes('organization'))
      .map(tx => this.mapMvtTransaction(tx));
  }

  private mapMvtTransaction(tx: any): any {
    const isOrganization = tx.toOrganization || tx.toOrganizationId || tx.metadata?.includes('organization');

    if (isOrganization) {
      const orgName = tx.toOrganization?.name || 'Organization';
      const orgId = tx.toOrganization?.id || tx.toOrganizationId || this.generateRandomId();
      const orgData = { ...(tx.toOrganization || {}) };

      return {
        ...tx,
        amountStatus: 'EARNED',
        createdAt: tx.createdAt || tx.timestamp || new Date().toISOString(),
        amount: tx.amount || 0,
        MVPTokens: tx.amount || 0,
        description: orgName || tx.primaryLabel || 'Organization Tokens',
        toUser: null,
        person: null,
        toOrganization: { id: orgId, name: orgName, ...orgData },
        organization: { id: orgId, name: orgName, ...orgData },
        type: 'TOKEN_ORGANIZATION',
        isFunding: false
      };
    }

    const givenName = tx.toUser?.givenName || '';
    const familyName = tx.toUser?.familyName || '';
    const fullName = [givenName, familyName].filter(Boolean).join(' ') || 'User';

    return {
      ...tx,
      amountStatus: 'EARNED',
      createdAt: tx.createdAt || tx.timestamp || new Date().toISOString(),
      amount: tx.amount || 0,
      MVPTokens: tx.amount || 0,
      description: tx.primaryLabel || 'Tokens Earned',
      toUser: tx.toUser ? { ...(tx.toUser || {}), name: fullName, givenName, familyName } : null,
      person: tx.toUser ? { name: fullName, givenName } : null,
      organization: null,
      type: 'TOKEN_EARNED',
      isFunding: false
    };
  }

  private extractFundingActivities(response: any): any[] {
    const items = response?.data?.cityFundTransactionsByDate?.items || [];
    return items
      .filter(tx => tx?.type !== 'tokenTransfer')
      .map(tx => ({
        ...tx,
        amountStatus: tx.amount > 0 ? 'CREDITED' : 'DEBITED',
        createdAt: tx.createdAt || new Date().toISOString(),
        name: tx.name || 'Unknown',
        type: tx.type || 'funding',
        isFunding: true
      }));
  }

  private generateRandomId(): string {
    return 'org-' + Math.random().toString(36).substr(2, 9);
  }

}
