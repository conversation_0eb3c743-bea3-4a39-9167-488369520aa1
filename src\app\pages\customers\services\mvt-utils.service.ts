import { Injectable } from '@angular/core';
import { Observable,throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { MvtWalletService } from './mvt-wallet.service';
import { ToastrService } from 'ngx-toastr';
import { Transaction, TransactionOperationType, TransactionStatus, TransactionType } from '../../../shared/types/transaction.types';

@Injectable({
  providedIn: 'root'
})
export class MvtUtilsService {
  constructor(
    private readonly mvtWalletService: MvtWalletService,
    private readonly toastr: ToastrService
  ) { }

  /**
   * Fetches MVT balance for a given address
   * @param address Wallet address
   * @param isAdmin Whether the request is from admin
   * @returns Observable with balance data
   */
  fetchMVTBalance(address: string, isAdmin: boolean): Observable<{ balance: number; address: string }> {
    return this.mvtWalletService.getMVTBalance(address, isAdmin).pipe(
      map((response: any) => {
        const data = response?.data?.getMVTBalance?.data;
        if (!data) {
          throw new Error('Invalid response structure');
        }
        return {
          balance: data.balance || 0,
          address: data.id || address
        };
      }),
      catchError(error => {
        console.error('Error fetching MVT balance:', error);
        this.toastr.error('Failed to fetch MVT balance');
        return throwError(() => error);
      })
    );
  }

  /**
   * Maps backend transaction data to frontend transaction interface
   * @param transactions Array of transactions from backend
   * @returns Mapped transactions array
   */
  mapTransactions(transactions: any[]): Transaction[] {
    if (!transactions?.length) {
      return [];
    }

    return transactions.map((tx: any) => ({
      id: tx.id,
      date: new Date(tx.createdAt || tx.timestamp),
      type: this.mapTransactionType(tx.transactionType || tx.type || 'ADDED'),
      originalType: tx.transactionType || tx.type,
      amount: tx.amount,
      currency: this.mapTokenType(tx.tokenType || tx.currency),
      status: this.mapTransactionStatus(tx.status),
      from: tx.fromUserId || tx.fromWalletId || tx.from,
      to: tx.toUserId || tx.toWalletId || tx.to,
      metadata: {
        ...tx.metadata,
        transactionHash: tx.transactionHash
      },
      fromUser: tx.fromUser,
      toUser: tx.toUser,
      adminUser: tx.adminUser,
      blockNumber: tx.blockNumber,
      gasUsed: tx.gasUsed,
      displayType: tx.displayType,
      primaryLabel: tx.primaryLabel,
      secondaryInfo: tx.secondaryInfo,
      showEtherscanLink: tx.showEtherscanLink,
      formattedDate: tx.formattedDate
    })).sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  /**
   * Maps transaction type from backend to frontend enum
   * @param type Transaction type from backend
   * @returns Mapped TransactionOperationType
   */
  private mapTransactionType(type: string): TransactionOperationType {
    const typeMap: { [key: string]: TransactionOperationType } = {
      'MINT': TransactionOperationType.ADDED,
      'TRANSFER': TransactionOperationType.SENT,
      'RECEIVED': TransactionOperationType.RECEIVED,
      'ADDED': TransactionOperationType.ADDED
    };

    return typeMap[type] || TransactionOperationType.ADDED;
  }

  /**
   * Handles common error cases for MVT operations
   * @param error Error object
   * @param defaultMessage Default error message
   */
  private mapTokenType(tokenType: string | undefined): TransactionType {
    if (!tokenType) return TransactionType.MVT;
    
    const normalizedType = tokenType.toLowerCase();
    if (normalizedType === TransactionType.MVT || normalizedType === TransactionType.USDC) {
      return normalizedType as TransactionType;
    }
    return TransactionType.MVT;
  }

  private mapTransactionStatus(status: string | undefined): TransactionStatus {
    if (!status) return TransactionStatus.COMPLETED;

    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'COMPLETE':
      case 'SUCCESS':
      case 'SUCCESSFUL':
        return TransactionStatus.COMPLETED;
      case 'PENDING':
      case 'PROCESSING':
      case 'IN_PROGRESS':
        return TransactionStatus.PENDING;
      case 'FAILED':
      case 'FAILURE':
      case 'ERROR':
        return TransactionStatus.FAILED;
      default:
        return TransactionStatus.COMPLETED;
    }
  }

  handleError(error: any, defaultMessage: string = 'An error occurred'): void {
    console.error('MVT Error:', error);
    const message = error?.error?.message || error?.message || defaultMessage;
    this.toastr.error(message);
  }
}
