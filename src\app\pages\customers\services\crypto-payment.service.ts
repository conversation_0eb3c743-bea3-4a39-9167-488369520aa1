import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  CREATE_CRYPTO_PAYMENT_SESSION,
} from '../graphql/crypto-payment-graphql.queries';
import { BaseApiService } from '../../../shared/services/base-api.service';

@Injectable({
  providedIn: 'root'
})
export class CryptoPaymentService {
  constructor(
    private baseApiService: BaseApiService
  ) { }

  /**
   * Create a new payment session for MVT token purchase with Stripe
   * @param mvtAmount Amount of MVT tokens to purchase (equal to USDC amount due to 1:1 ratio)
   * @param walletAddress User's blockchain wallet address to receive tokens
   * @returns Observable with session details including checkout URL
   */
  createStripePaymentSession(mvtAmount: string, walletAddress: string): Observable<any> {
    return this.baseApiService.customQuery(
      CREATE_CRYPTO_PAYMENT_SESSION,
      { mvtAmount, walletAddress }
    ).pipe(
      map((response: any) => {
        if (response?.createCryptoPaymentSession?.statusCode !== 200) {
          throw new Error(response?.createCryptoPaymentSession?.message || 'Failed to create payment session');
        }
        return response.createCryptoPaymentSession.data;
      }),
      catchError(error => {
        console.error('Error creating payment session:', error);
        return throwError(() => new Error(error?.message || 'An unexpected error occurred'));
      })
    );
  }

  /**
   * Handle a Stripe payment notification webhook event
   * @param event Stripe webhook event
   * @returns Observable with processing result
   */
  handleStripeWebhook(event: any): Observable<any> {
    const webhookMutation = `
      mutation HandleStripeWebhook($event: AWSJSON!) {
        handleStripeWebhook(event: $event) {
          statusCode
          message
          data
        }
      }
    `;

    return this.baseApiService.customQuery(
      webhookMutation,
      { event: JSON.stringify(event) }
    ).pipe(
      map((response: any) => {
        if (response?.handleStripeWebhook?.statusCode !== 200) {
          throw new Error(response?.handleStripeWebhook?.message || 'Failed to process webhook');
        }
        return response.handleStripeWebhook.data;
      }),
      catchError(error => {
        console.error('Error handling webhook:', error);
        return throwError(() => new Error(error?.message || 'Failed to process webhook'));
      })
    );
  }

  /**
   * Validate a payment ID to verify it's a valid format
   * @param paymentId Stripe payment ID
   * @returns boolean indicating validity
   */
  isValidPaymentId(paymentId: string): boolean {
    return paymentId.startsWith('cs_') || paymentId.startsWith('cos_');
  }
}