import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { Auth, Storage } from 'aws-amplify';
import { TitleCasePipe } from '@angular/common';
import { NgxSpinnerService } from 'ngx-spinner';
import * as XLSX from 'xlsx';

import { FilterPipe } from '../../pipes/filter-pipe';
import { SharedService } from '../../../../shared/services/shared.service';
import { HomeworkService } from '../../service/homework.service';
import { homeworkImportValidationRules } from '../../../../shared/models/import-validation-rules.model';
import { ModuleName } from '../../../../shared/enums/module-name.enum';


@Component({
  selector: 'app-homework-list',
  templateUrl: './homework-list.component.html',
  styleUrls: ['./homework-list.component.scss']
})
export class HomeworkListComponent implements OnInit {
  @Input() organizationId: string = '';
  @Input() personId: string = '';

  programId: string = '';
  exportType: string = '';
  showOptions: string = '';
  programName: string = '';
  organizationName: string = '';
  currentTab: string = 'Table Panel';
  sortBy: string = 'Recently Updated';

  filters: any;
  programsList: any[] = [];

  _version: number;
  programsCount: number = 0;
  currentUser: any;
  file: File | null = null;
  validationResults: string[] = [];
  personName: string;

  constructor(
    private readonly router: Router,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly filterPipe: FilterPipe,
    private readonly titleCasePipe: TitleCasePipe,
    public sharedService: SharedService,
    public homeWorkService: HomeworkService,
    private readonly spinner: NgxSpinnerService
  ) { }

  ngOnInit() {
    this.getHomeWorkList();

    if (this.homeWorkService.filters) {
      this.filters = this.homeWorkService.filters;
    }
  }

  /**
   * * Set Filters Homework
   * ? This function is used for setting filters emitted from child component 'homework-filter' and redirecting to homework profile page.
   * @param id This parameter holds the Id of the homework in question.
   */
  setFiltersHomeWork(id: string) {
    this.homeWorkService.filters = this.filters;
    this.homeWorkService.currentPage = this.currentPage;

    this.router.navigate(['/activities/view-activities', id]);
  }

  /**
   * * Open Delete Confirmation Modal
   * ? This function is used for triggering the confirmation modal to open.
   * @param content This parameter holds teh content data of the modal.
   * @param id This parameter holds the homework Id.
   * @param programName This parameter holds the homework name.
   * @param version This parameter holds the homework version.
   */
  openDeleteConfirmationModal(
    content: any,
    id: string,
    programName: string,
    version: number
  ) {
    this.programId = id;
    this.programName = programName;
    this._version = version;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  /**
   * * Close Delete Confirmation Modal
   * ? This function is used for closing open modal.
   */
  closeDeleteConfirmationModal() {
    this.modalService.dismissAll();
  }

  /**
   * * Set Current Tab
   * ? This function is used for setting current tab value.
   * @param tab 
   */
  setCurrentTab(tab: string) {
    this.currentTab = tab;
  }

  /**
   * * Get Homework List
   * ? This function is used for fetching the list of all the homework.
   */
  getHomeWorkList() {
    this.sharedService.isLoading.next(true);
    this.spinner.show();
    if (this.organizationId) {
      this.homeWorkService.getOrganizationById(this.organizationId).subscribe({
        next: ({ data }: any) => {
          this.programsList = data?.getOrganizations?.homeworks?.items
            ?.filter(
              (element: any) =>
                !element?.homeworkData?._deleted &&
                element?.homeworkData?.cityId ===
                  this.sharedService?.defaultCityId?.value
            )
            .map((iterator: any) => {
              let result = { ...iterator, ...iterator.homeworkData };
              delete result.homeworkData;
              return result;
            }) || [];
          this.programsCount = this.programsList.length;

          if (this.organizationId) {
            this.organizationName = this.titleCasePipe.transform(
              this.programsList[0]?.organization?.name
            );
          }

          this.programsList.forEach(element => {
            if (element?.imageUrl) {
              Storage.get(element.imageUrl, {
                level: 'public',
              }).then((result: string) => {
                element.imagePreview = result;
              });
            }
          })

          this.applyFilter(this.filters, this.homeWorkService.currentPage || 1);
          this.sharedService.isLoading.next(false);
          this.spinner.hide();
        }
      })
    } else if (this.personId) {
      this.homeWorkService.getUserById(this.personId).subscribe({
        next: ({ data }: any) => {
          this.programsList = data.getUser?.homeworks?.items.filter(
            (element: any) =>
              !element?.homeworkData?._deleted &&
              element?.homeworkData?.cityId === this.sharedService?.defaultCityId?.value
          ).map((iterator: any) => {
            let result = { ...iterator, ...iterator.homeworkData };
            delete result.homeworkData;
            return result;
          });
          this.programsCount = this.programsList.length;

          if (this.personId) {
            this.personName = this.titleCasePipe.transform(
              this.programsList[0]?.organization?.name
            );
          }

          this.programsList.forEach(element => {
            if (element?.imageUrl) {
              Storage.get(element.imageUrl, {
                level: 'public',
              }).then((result: string) => {
                element.imagePreview = result;
              });
            }
          })

          this.applyFilter(this.filters, this.homeWorkService.currentPage || 1);
          this.sharedService.isLoading.next(false);
          this.spinner.hide();
        }
      })
    }
    else {
      this.homeWorkService.getHomeWorkList(this.organizationId || '')
        .subscribe({
          next: ({ data }: any) => {
            this.programsList = data.homeworkByDate.items.filter(
              (element: any) =>
                !element?._deleted &&
                element.cityId === this.sharedService?.defaultCityId?.value
            );

            this.programsCount = this.programsList.length;

            if (this.organizationId) {
              this.organizationName = this.titleCasePipe.transform(
                this.programsList[0]?.organization?.name
              );
            }

            this.programsList.forEach(element => {
              if (element?.imageUrl) {
                Storage.get(element.imageUrl, {
                  level: 'public',
                }).then((result: string) => {
                  element.imagePreview = result;
                });
              }
            })

            this.applyFilter(this.filters, this.homeWorkService.currentPage || 1);
            this.sharedService.isLoading.next(false);
            this.spinner.hide();

          },
        });
    }
  }

  /**
   * * Get Current Page
   * ? This function is used for getting current page value.
   */
  get currentPage() {
    return this.sharedService.currentPage.value;
  }

  /**
   * * Get Count Start Value
   * ? This function is used for getting homework count start index on each page of list. 
   */
  get getCountStartValue() {
    return this.sharedService.getCountStartValue(this.programsCount);
  }

  /**
  * * Get Count End Value
  * ? This function is used for getting homework count end index on each page of list. 
  */
  get getCountEndValue() {
    return this.sharedService.getCountEndValue(this.programsCount);
  }

  /**
   * * Apply Filter
   * ? This function is used for applying the selected filters onto the list of homeworks
   * @param value This parameter holds the filters to be applied on homework-list.
   * @param currentPage This parameter holds the value of current page.
   */
  applyFilter(value: any, currentPage?: number) {
    this.filters = value;

    this.programsCount = this.filterPipe.transform(
      this.programsList,
      this.filters,
      this.sortBy
    ).length;

    this.sharedService.currentPage.next(currentPage || 1);
  }

  /**
   * * Get Random Color
   * ? This function is used to get a ranom color.
   * @param index This parameter holds the index value for returning a color.
   * @returns This functions returns a hex code of a random color.
   */
  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  /**
   * * Delete Homework
   * ? This function is used for deleting a particular homework.
   */
  deleteHomework() {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    this.homeWorkService
      .getHomeWorkById(this.programId)
      .subscribe((res: any) => {
        let memberRelationIds: any = []
        let userRelationIds: any = []
        res?.data?.getHomework?.members.items?.map((ex: any) => {
          memberRelationIds.push(ex.id)
        })
        res?.data?.getHomework?.studentsStakeholders.items?.map((ex: any) => {
          userRelationIds.push(ex.id)
        })
        this.homeWorkService.deleteHomeworkRelations({
          memberRelationIds: memberRelationIds,
          userRelationIds: userRelationIds
        }).subscribe({});
        this.homeWorkService
          .deleteHomework(this.programId, this._version)
          .subscribe({
            next: ({ data }: any) => {
              this.sharedService
                .generateLog({
                  type: 'DELETED',
                  moduleId: this.programId,
                  moduleName: data?.deleteHomework?.name,
                  moduleType: 'homework',
                  requestStatus: 'SYSTEM_APPROVED',
                  activityType: 'MEMBERSHIP',
                  cityId: this.sharedService.defaultCityId.value,
                })
                .subscribe();
              this.toastr.success('Successfully deleted assignment!');

              this.programsList = this.programsList.filter(
                (element) => element.id !== this.programId
              );

              this.filterPipe.transform(
                this.programsList,
                this.filters,
                this.sortBy
              );

              this.programsCount = this.filterPipe.transform(
                this.programsList,
                this.filters,
                this.sortBy
              ).length;

              this.sharedService.currentPage.next(1);

              this.closeDeleteConfirmationModal();
              this.sharedService.isLoading.next(false);
              this.spinner.hide();

            },
            error: (error: any) => {
            },
          });
      });
  }

  /**
   * * Export Homeworks
   * ? This function is used for exporting the list of homeworks as a pdf or a excel file.
   * @param event This parameter holds the value of the type of file to exported.
   */
  exportHomeworks(event: any) {
    if (event.value === 'excel') {
      this.homeWorkService.exportExcel(this.organizationName);
    }

    if (event.value === 'pdf') {
      this.homeWorkService.exportPDF(
        this.filterPipe.transform(this.programsList, this.filters, this.sortBy),
        this.organizationName
      );
    }

    event.value = '';
  }

  // Function to map the record field
  mapRecordField(record: any, fieldMapping: string, defaultValue: string): any {
    return fieldMapping in record ? record[fieldMapping] : record[defaultValue];
  }

  // Function to process the record and return the structured object
  processRecord(record: any): any {
    return {
      "name": this.mapRecordField(record, 'Name', 'name'),
      "dueDate": this.mapRecordField(record, 'Due Date', 'dueDate'),
      "shortDescription": this.mapRecordField(record, 'Short Description', 'shortDescription'),
      "longDescription": this.mapRecordField(record, 'Long Description', 'longDescription') ?? "",
      "cityId": this.mapRecordField(record, 'Village', 'cityId'),
      "assignmentType": this.mapRecordField(record, 'Assignment Type', 'assignmentType'),
      "entityType": this.mapRecordField(record, 'Entity Type', 'entityType'),
      "memberIds": this.mapRecordField(record, 'Member Ids', 'memberIds'),
      "stakeholderIds": this.mapRecordField(record, 'Stakeholder Ids', 'stakeholderIds'),
      "studentIds": this.mapRecordField(record, 'Student Ids', 'studentIds'),
      "coCreationType": this.mapRecordField(record, 'Co Creation Type', 'coCreationType') ?? "",
      "microcredentialId": this.mapRecordField(record, 'Microcredential Id', 'microcredentialId') ?? "",
      "assignmentPoints": this.mapRecordField(record, 'Assignment Points', 'assignmentPoints'),
      "contactFullName": this.mapRecordField(record, 'Contact Full Name', 'contactFullName'),
      "contactEmail": this.mapRecordField(record, 'Contact Email', 'contactEmail'),
      "contactPhoneNumber": this.mapRecordField(record, 'Contact Phone Number', 'contactPhoneNumber'),
      "contactRole": this.mapRecordField(record, 'Contact Role', 'contactRole')
    };
  }

  /**
   * * On File Select
   * ? This function is used for uploading user selected file for uploading process.
   * ? Scenario check while import Assignments(homework)
   * ? 1. If same Assignments and different stakeholder added in the sheet then it will add the 2 Assignments and 2 stakeholders
   * ? 2. If same Assignments and same stakeholder added in the sheet then it will add the 1 Assignment and 1 stakeholder
   * ? 3. If different Assignments and different stakeholder added in the sheet then it will add the 2 Assignments and 2 stakeholders
   * ? 4. If different Assignments and same stakeholder added in the sheet then it will add the 1 Assignment and 1 stakeholder
   * @param ev This parameter holds the file data of the selected file.
   * 
   */
  onFileSelect(ev: any): void {
    this.spinner.show();
    this.file = ev.target.files[0];
    if (!this.file) {
      this.validationResults.push('Please upload an Excel file first.');
      return;
    }

    const reader = new FileReader();

    reader.onload = async (e: any) => {
      try {
        const arrayBuffer = e.target.result;
        const binaryData = this.arrayBufferToBinaryString(arrayBuffer); // Convert arrayBuffer to binary string
        const wb = XLSX.read(binaryData, { type: 'binary' });
        const ws = wb.Sheets[wb.SheetNames[0]];
        const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
        const payload = wb.SheetNames.reduce((initial: any, name: any) => {
          const sheet = wb.Sheets[name];
          initial['items'] = XLSX.utils.sheet_to_json(sheet);
          return initial;
        }, {});

        let ref: any = {
          "name": "name",
          "imageUrl": "imageUrl",
          "dueDate": "dueDate",
          "shortDescription": "shortDescription",
          "longDescription": "longDescription",
          "cityId": "cityId",
          "assignmentType": "assignmentType",
          "entityType": "entityType",
          "memberIds": "memberIds",
          "stakeholderIds": "stakeholderIds",
          "studentIds": "studentIds",
          "coCreationType": "coCreationType",
          "microcredentialId": "microcredentialId",
          "assignmentPoints": "assignmentPoints",
          "contactFullName": "contactFullName",
          "contactEmail": "contactEmail",
          "contactPhoneNumber": "contactPhoneNumber",
          "contactRole": "contactRole",
          "Name": "name",
          "Due Date": "dueDate",
          "Short Description": "shortDescription",
          "Long Description": "longDescription",
          "Village": "cityId",
          "Assignment Type": "assignmentType",
          "Entity Type": "entityType",
          "Member Ids": "memberIds",
          "Stakeholder Ids": "stakeholderIds",
          "Student Ids": "studentIds",
          "Co Creation Type": "coCreationType",
          "Microcredential Id": "microcredentialId",
          "Assignment Points": "assignmentPoints",
          "Contact Full Name": "contactFullName",
          "Contact Email": "contactEmail",
          "Contact Phone Number": "contactPhoneNumber",
          "Contact Role": "contactRole"
        }
        // change heading row to DB keys in data.
        let headingRow: any = data[0];
        headingRow = headingRow.map((title: any) => { return ref[title] });
        data[0] = headingRow;

        const jsonData = {
          items: payload.items.map((record: any) => this.processRecord(record))
        };

        let importedList = jsonData['items'];

        importedList.forEach((element: any) => {
          if (element.dueDate) {
            const dateNumber = element.dueDate; // The number representing the date
            const date = new Date((dateNumber - 25569) * 86400 * 1000); // Convert to JavaScript date object
            const dateString = date.toISOString(); // Convert to ISO string and extract YYYY-MM-DD
            element.dueDate = dateString;
          }
        });

        const result = this.sharedService.importSheetValidations(data, homeworkImportValidationRules);
        this.validationResults = result.errors;
        this.currentUser = await Auth.currentAuthenticatedUser().then(
          (data: any) => {
            return data;
          }
        );
        jsonData['createdUserId'] = this.currentUser.attributes.sub;
        jsonData['createdUserName'] = this.currentUser.attributes.name;
        jsonData['cityId'] = this.sharedService?.defaultCityId.value;
        jsonData['items'] = importedList;

        const { items, hasDuplicates } = this.sharedService.removeDuplicates(jsonData, ModuleName.ASSIGNMENTS);

        if (this.validationResults.length === 0) {
          this.sharedService?.uploadAssignmentsFile({ items }).subscribe({
            next: (res: any) => {
              //get list method
              this.sharedService.importMetaData.next({
                module: 'assignment',
                count: res?.data?.importAssignments?.data?.fail?.length,
                data: res?.data?.importAssignments?.data?.fail.length > 0 ? res?.data?.importAssignments?.data?.fail : [],
                fromAPI: res?.data?.importAssignments?.data?.fail.length > 0,
                successCount: res?.data?.importAssignments?.data?.success
              });
              this.router.navigate(['/import-file'], { queryParams: { hasDuplicates: hasDuplicates } });
              this.spinner.hide();
            },
            error: () => { this.spinner.hide(); },
          });
        } else {
          this.sharedService.importMetaData.next({
            count: result.errorRowCount,
            module: 'assignment',
            data: this.validationResults
          });
          this.router.navigate(['/import-file'], { queryParams: { hasDuplicates: hasDuplicates } });
          this.file = null;
          this.spinner.hide();
        }
      } catch (error: any) {
        this.validationResults.push('Error reading the file: ' + error.message);
        this.file = null;
      }
    };

    reader.onerror = (error: any) => {
      this.validationResults.push('Error reading the file: ' + error.message);
      this.file = null;
    };

    reader.readAsArrayBuffer(this.file);
  }

  arrayBufferToBinaryString(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return binary;
  }

  downloadBlob(blob: any, filename: any) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename ?? 'download';
    const clickHandler = () => {
      setTimeout(() => {
        URL.revokeObjectURL(url);
        a.removeEventListener('click', clickHandler);
      }, 150);
    };
    a.addEventListener('click', clickHandler, false);
    a.click();
    return a;
  }

  /**
  * * Download File
  * ? This function is used for downloading the sample file used in importing schools.
  */
  async downloadFile() {
    try {
      let key = 'homework/Import-Assignments.xlsx';
      const result: any = await Storage.get(key, { download: true });
      this.downloadBlob(result.Body, "Import-Event");
    } catch (error) {
      console.error(error)
      this.toastr.error('Error while fetching this document.');
    }
  }
}
