import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  PROCESS_ONRAMP_TRANSFER,
  CHECK_ONRAMP_TRANSFER_STATUS,
  VERIFY_ONRAMP_SESSION
} from '../graphql/mvt-wallet-graphql.queries';
import { ErrorHandlerService } from '../../../shared/services/error-handler.service';
import { BaseApiService } from '../../../shared/services/base-api.service';
import {
  isStandardizedErrorResponse,
  ErrorSeverity
} from '../../../shared/interfaces/standardized-error.interface';

export interface MVTTransferRequest {
  userId: string;
  mvtAmount: number;
  usdcAmount: number;
  exchangeRate: number;
  sessionId: string;
  description?: string;
}

export interface MVTTransferResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class MvtTransferService {

  constructor(
    private errorHandler: ErrorHandlerService,
    private baseApiService: BaseApiService
  ) { }

  /**
   * Process MVT transfer after successful USDC purchase
   */
  processMVTTransfer(transferRequest: MVTTransferRequest): Observable<MVTTransferResponse> {

    if (!transferRequest.userId || !transferRequest.mvtAmount || !transferRequest.sessionId) {
      return throwError(() => new Error('Missing required transfer parameters'));
    }

    const variables = {
      userId: transferRequest.userId,
      mvtAmount: transferRequest.mvtAmount,
      usdcAmount: transferRequest.usdcAmount,
      exchangeRate: transferRequest.exchangeRate,
      sessionId: transferRequest.sessionId,
      description: transferRequest.description || `USDC purchase: ${transferRequest.usdcAmount} USDC converted to ${transferRequest.mvtAmount} MVT (rate: ${transferRequest.exchangeRate})`
    };

    return this.baseApiService.mutateData(
      PROCESS_ONRAMP_TRANSFER,
      { input: variables }
    ).pipe(
      map((result: any) => {

        if (result.data?.processOnrampTransfer?.statusCode === 200) {
          return {
            success: true,
            transactionId: result.data.processOnrampTransfer.data?.mvtTransactionId,
            message: result.data.processOnrampTransfer.message,
            data: result.data.processOnrampTransfer.data
          };
        } else {
          throw new Error(result.data?.processOnrampTransfer?.message || 'Transfer failed');
        }
      }),
      catchError(error => {
        console.error('Error processing MVT transfer:', error);

        if (isStandardizedErrorResponse(error)) {
          const userMessage = this.errorHandler.handleError(error, {
            showToast: false,
            logToConsole: true,
            severity: ErrorSeverity.HIGH
          });
          return throwError(() => new Error(userMessage.message));
        }

        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.HIGH
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }

  /**
   * Verify Stripe onramp session completion
   */
  verifyOnrampSession(sessionId: string): Observable<any> {

    return this.baseApiService.queryWithCityFilter(
      VERIFY_ONRAMP_SESSION,
      { sessionId },
      1,
      false
    ).pipe(
      map((result: any) => {
        return result.data?.verifyOnrampSession;
      }),
      catchError(error => {
        console.error('Error verifying onramp session:', error);

        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.MEDIUM
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }

  /**
   * Check if transfer was already processed for this session
   */
  checkTransferStatus(sessionId: string): Observable<boolean> {

    return this.baseApiService.queryWithCityFilter(
      CHECK_ONRAMP_TRANSFER_STATUS,
      { sessionId },
      1,
      false
    ).pipe(
      map((result: any) => {
        const response = result.data?.checkOnrampTransferStatus;
        return response?.data?.processed || false;
      }),
      catchError(error => {
        console.error('Error checking transfer status:', error);

        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.MEDIUM
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }
}
