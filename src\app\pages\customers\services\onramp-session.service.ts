import { Injectable } from '@angular/core';

export interface OnrampSessionData {
  sessionId: string;
  userId: string;
  mvtAmount: number;
  usdcAmount: number;
  exchangeRate: number;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
  clientSecret?: string;
}

@Injectable({
  providedIn: 'root'
})
export class OnrampSessionService {
  private readonly STORAGE_KEY = 'mvt_onramp_session';
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000;

  constructor() {}

  /**
   * Store onramp session data before redirecting to Stripe
   */
  storeSessionData(sessionData: OnrampSessionData): void {
    try {
      const dataWithTimestamp = {
        ...sessionData,
        timestamp: Date.now(),
        status: 'pending' as const
      };
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(dataWithTimestamp));
    } catch (error) {
      console.error('Error storing onramp session data:', error);
    }
  }

  /**
   * Retrieve stored session data
   */
  getSessionData(): OnrampSessionData | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const sessionData: OnrampSessionData = JSON.parse(stored);
      
      if (Date.now() - sessionData.timestamp > this.SESSION_TIMEOUT) {
        this.clearSessionData();
        return null;
      }

      return sessionData;
    } catch (error) {
      console.error('Error retrieving onramp session data:', error);
      return null;
    }
  }

  /**
   * Update session status
   */
  updateSessionStatus(status: 'pending' | 'completed' | 'failed'): void {
    try {
      const sessionData = this.getSessionData();
      if (sessionData) {
        sessionData.status = status;
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessionData));
      }
    } catch (error) {
      console.error('Error updating session status:', error);
    }
  }

  /**
   * Clear session data (after successful processing or expiration)
   */
  clearSessionData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing onramp session data:', error);
    }
  }

  /**
   * Check if there's a pending session that needs processing
   */
  hasPendingSession(): boolean {
    const sessionData = this.getSessionData();
    return sessionData?.status === 'pending';
  }

  /**
   * Get session ID for tracking
   */
  getCurrentSessionId(): string | null {
    const sessionData = this.getSessionData();
    return sessionData?.sessionId || null;
  }

  /**
   * Check if session was already processed to prevent duplicates
   */
  isSessionProcessed(): boolean {
    const sessionData = this.getSessionData();
    return sessionData?.status === 'completed';
  }
}
