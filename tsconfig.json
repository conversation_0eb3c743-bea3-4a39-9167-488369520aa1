{
  "compileOnSave": false,
  "compilerOptions": {
    "skipLibCheck": true,
    "allowJs": true,
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    "strict": false,
    "strictPropertyInitialization": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "es2020",
    "lib": [
      "es2023",
      "dom",
      "esnext.asynciterable"
    ],
    "useDefineForClassFields": false,
    "paths": {
      "src/*": ["src/*"]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": false,
    "strictTemplates": false, //check this line for strict
  }
}