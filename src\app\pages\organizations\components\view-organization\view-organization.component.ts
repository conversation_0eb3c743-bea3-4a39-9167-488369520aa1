import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { Storage } from 'aws-amplify';
import { forkJoin, map, lastValueFrom, switchMap, of, Observable } from 'rxjs';
import { FormControl, Validators } from '@angular/forms';

import { EventsService } from './../../../events/services/events.service';
import { TasksService } from 'src/app/pages/tasks/services/tasks.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { OrganizationsService } from '../../services/organizations.service';
import { PersonToPersonService } from 'src/app/pages/person-to-person/services/person-to-person.service';
import { ActivitiesService } from 'src/app/pages/activities/services/activities.service';


@Component({
  selector: 'app-view-organization',
  templateUrl: './view-organization.component.html',
  styleUrls: ['./view-organization.component.scss'],
})
export class ViewOrganizationComponent implements OnInit {
  filters: any;
  documents: any;
  organizationDetails: any;
  sortBy: string = 'Recently Updated';
  statusPercentage: any;
  currentPage: number;
  showOptions: string = '';
  _version: number;
  organizationId: string = '';
  organizationName: string = '';
  currentTab: string = 'overview';
  associationList: any[] = [];
  filteredAssociationList: any[] = [];
  totalMembers: any[] = [];
  organizationsEventsIdVersion: any[] = [];
  tasksIdVersion: any[] = [];
  totalTokens: number = 0;
  relationshipsCount: number = 0;
  allTransactions: any[] = [];
  eventsList: any[] = [];
  memberPercentageList: any[] = [];
  chartColor: any;
  allUsers: any[] = [];
  colors: any = {
    red: {
      primary: '#bc4749',
      secondary: '#FAE3E3',
    },
    blue: {
      primary: '#43bccd',
      secondary: '#D1E8FF',
    },
    yellow: {
      primary: '#f9c80e',
      secondary: '#FDF1BA',
    },
    green: {
      primary: '#a7c957',
      secondary: '#cafaca',
    },
    purple: {
      primary: '#662e9b',
      secondary: '#facdfa',
    },
    orange: {
      primary: '#f86624',
      secondary: '#ffe1ab',
    },
  };

  fileCount: any = '';
  pointsData: any[] = [];
  organizationDocuments: any;
  folderList: any = []
  defaultfolderList: any = [
    { category: 'Membership MOUs & Contracts', updateDate: '', name: 'Membership MOUs & Contracts' },
    { category: 'Member Organization Logos', updateDate: '', name: 'Member Organization Logos' },
    { category: 'Recorded Knowledge Interviews', updateDate: '', name: 'Recorded Knowledge Interviews' },
    { category: 'Project & Program Photos', updateDate: '', name: 'Project & Program Photos' }
  ];
  associationCommunities: any = {
    "COMMUNITY_FOUNDATION_FOR_NORTHEAST_FLORIDA": "Community Foundation for Northeast Florida",//Jacksonville
    "COMMUNITY_FOUNDATION_TAMPA_BAY": "Community Foundation Tampa Bay",//Tampa
    "CENTRAL_FLORIDA_FOUNDATION": "Central Florida Foundation",//Orlando
    "COMMUNITY_FOUNDATION_OF_NORTH_CENTRAL_FLORIDA": "Community Foundation of North Central Florida",//Gainesville
    "MIAMI_COMMUNITY_FOUNDATION": "Miami Community Foundation"//Miami
  }

  isFolder: boolean = true;
  categoryLatest: any = {
    'Membership MOUs & Contracts': '',
    'Member Organization Logos': '',
    'Recorded Knowledge Interviews': '',
    'Project & Program Photos': ''
  };
  categoryFileSize: any = {
    'Membership MOUs & Contracts': '',
    'Member Organization Logos': '',
    'Recorded Knowledge Interviews': '',
    'Project & Program Photos': ''
  };
  searchKey: string = '';
  currentSelectedCategory: any = '';
  files: File[] = [];
  documentCategory = new FormControl('', [Validators.required]);
  showCategoryDocuments: boolean = false;
  currentCategoryDocuments: any = [];
  categoryDocuments: any = {
    'Membership MOUs & Contracts': '',
    'Member Organization Logos': '',
    'Recorded Knowledge Interviews': '',
    'Project & Program Photos': ''
  };
  categoryModal: boolean = false;
  removeDocumentList: any = [];
  otherDocuments: any = [];
  version: any;
  listCategories: any = [];
  categoryWisePoints: any = {};
  allPointLogs: any = [];
  cityFundData: any = [];
  userList: any = [];
  totalFunds: any = 0;
  mvtWalletBalance: number = 0;
  lockedMVTBalance: number = 0;
  availableMVTBalance: number = 0;
  isFetchingMVTBalance: boolean = false;
  mvtWalletError: string | null = null;

  constructor(
    private readonly router: Router,
    private readonly location: Location,
    private readonly modalService: NgbModal,
    private readonly spinner: NgxSpinnerService,
    private readonly tasksService: TasksService,
    private readonly eventsService: EventsService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    public readonly sharedService: SharedService,
    private readonly organizationsService: OrganizationsService,
    private readonly personToPersonService: PersonToPersonService,
    public activitiesService: ActivitiesService
  ) {
    this.sharedService.listCategories().subscribe({
      next: (response: any) => {
        this.listCategories = response?.data?.listCategories?.items.filter((type: any) => !type.parentId).map((category: any) => category.name);
      }
    })
  }

  ngOnInit(): void {
    this.organizationId = this.sharedService.getDecryptedId(
      this.activatedRoute.snapshot.paramMap.get('id')
    );
    this.getRelationshipsList();
    if (this.organizationsService.filters) {
      this.filters = this.organizationsService.filters;
    }

    if (this.organizationsService.currentPage) {
      this.currentPage = this.organizationsService.currentPage;
    }


    this.sharedService.getUserListStripe().subscribe({
      next: (response: any) => {
        let users = response?.data?.userByDate?.items;
        users.forEach((user: any) => {
          if (user?.customerId != null) {
            this.userList[user?.customerId] = user;
          }
        })
        this.getAllTokens();
      }
    })

    this.getAllMembers();
    this.getOrganizationDetails();
    this.getOrganizationRelationDetails();
    this.getMemberById();
    if (this.organizationId) {
      this.getEventsList();
    }
    this.folderList = this.defaultfolderList;
    this.getPointsData();
  }

  getPointsData(nextToken?: string) {
    this.sharedService.getAllPointsNextToken(nextToken).subscribe({
      next: (response: any) => {
        if (response?.data?.pointsByDate?.items) {
          this.allPointLogs.push(...response.data.pointsByDate.items);
        }
        if (response?.data?.pointsByDate?.nextToken === null) {
          let data: any = {};
          this.allPointLogs.map((point: any) => {
            if (this.organizationId === point?.memberId) {
              if (data.hasOwnProperty(point?.category)) {
                data[point?.category] = data[point?.category] + Number(point?.impactScore);
              } else {
                data[point?.category] = Number(point?.impactScore);
              }
            }
          });
          this.categoryWisePoints = data;
        } else {
          this.getPointsData(response?.data?.pointsByDate?.nextToken);
        }
      }
    })
  }

  sumPointsById(items: any[]): any[] {
    const result: any[] = [];
    const map = new Map<string, any>();

    for (const item of items) {
      const memberId = item.memberId;
      const impactScore = Math.floor(item.impactScore);

      if (isNaN(impactScore)) continue;

      if (map.has(memberId)) {
        this.updateMemberImpactScore(map.get(memberId), impactScore, item.status);
      } else {
        const newMember = this.createNewMember(item, impactScore);
        map.set(memberId, newMember);

        if (this.isValidMember(newMember)) {
          result.push(newMember);
        }
      }
    }

    return result;
  }

  private updateMemberImpactScore(member: any, score: number, status: string): void {
    let currentScore = Math.floor(member.impactScore);
    if (status === 'CREDITED') {
      currentScore += score;
    } else if (status === 'DEBITED') {
      currentScore -= score;
    }
    member.impactScore = currentScore;
  }

  private createNewMember(item: any, impactScore: number): any {
    return { memberId: item.memberId, impactScore, ...item };
  }

  private isValidMember(member: any): boolean {
    return (
      (member.organization && !member.organization._deleted) ||
      (member.person && !member.person._deleted)
    );
  }



  getEventsList(): void {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    this.eventsService
      .getEventsList(this.organizationId)
      .pipe(map((val) => this.processEventList(val)))
      .subscribe(({ data }: any) => {
        this.sharedService.isLoading.next(false);
        this.spinner.hide();

        if (this.organizationId) {
          this.eventsList = data?.listEventsOrganizations?.items?.filter(
            (event: any) => !event._deleted
          );
        }
      });
  }

  private processEventList(val: any): any {
    if (!this.organizationId) return val;

    const eventDataItems = val?.data?.listEventsOrganizations?.items
      ?.filter((item: any) => !item._deleted)
      .map((item: any) => item.events);

    if (Array.isArray(eventDataItems)) {
      eventDataItems.forEach((event: any) => {
        this.cleanDeletedEventOrgs(event);
        this.setCityNameForEvent(event);
        this.formatPhoneNumber(event);
      });
    }

    if (val?.data?.listEventsOrganizations) {
      val.data.listEventsOrganizations.items = eventDataItems || [];
    }
    return val;
  }

  private cleanDeletedEventOrgs(event: any): void {
    event.EventsOrganizations.items = event?.EventsOrganizations?.items?.filter(
      (subElement: any) => !subElement?._deleted
    );
  }

  private setCityNameForEvent(event: any): void {
    this.sharedService?.cityList?.subscribe((cities: any[]) => {
      const match = cities.find((city) => city.id === event.cityId);
      if (match) {
        event.city = match.name;
      }
    });
  }

  private formatPhoneNumber(event: any): void {
    if (event?.contactPhoneNumber?.length >= 10) {
      event.contactPhoneNumber = event.contactPhoneNumber.slice(-10);
    }
  }




  getMemberById(): void {
    this.sharedService
      .getMember(this.organizationId, 'organization')
      .subscribe({
        next: (res: any) => {
          this.totalTokens = res?.data?.membershipByDate?.items[0]?.MVPTokens;
        },
      });
  }

  async getAllMembers(nextToken?: any): Promise<void> {
    try {
      const res: any = await lastValueFrom(
        this.sharedService.getAllPointsFilter(nextToken)
      );

      this.pointsData.push(...(res?.data?.pointsByDate?.items ?? []));

      if (res?.data?.pointsByDate?.nextToken !== null) {
        await this.getAllMembers(res?.data?.pointsByDate?.nextToken);
      } else {
        this.getAllPointsData([...new Set(this.pointsData)]);
      }
    } catch (error) {
      console.error('Failed to fetch members:', error);
    }
  }

  getAllPointsData(res: any) {
    const now = new Date();
    let diffInMilliSeconds: any;
    let diffInDays: any;
    this.allTransactions = res?.filter(
      (element: any) => !element?._deleted
    );
    let totalPoint: number = 0;
    this.memberPercentageList = this.sumPointsById(
      res
    );



    this.memberPercentageList = this.memberPercentageList.filter(
      (element: any) => element?.memberId === this.organizationId
    );


    this.memberPercentageList.forEach((memberElement: any) => {
      // Parse impact score once
      const impactScore = parseInt(memberElement?.impactScore);

      // Calculate time frame points based on member type (organization or person)
      const createdAt = memberElement?.type !== 'student'
        ? new Date(memberElement?.organization?.createdAt)
        : new Date(memberElement?.person?.createdAt);

      const diffInMilliSeconds = now.getTime() - createdAt.getTime();
      const diffInDays = Math.round(diffInMilliSeconds / (24 * 60 * 60 * 1000));
      const timeFramePoints = diffInDays * 2;

      let percentage: number = 0;

      // If impact score is 0 or timeFramePoints is 0, set percentage to 0 and assign 'danger' color
      if ((impactScore === 0 && timeFramePoints === 0) || (impactScore !== 0 && timeFramePoints === 0)) {
        memberElement.percentageWithTimeAllTime = 0;
        this.chartColor = 'danger';
      } else {
        // Calculate percentage
        percentage = (impactScore / timeFramePoints) * 100;
        memberElement.percentageWithTimeAllTime = parseInt(percentage.toString());

        // Set chart color based on percentage ranges
        if (percentage >= 90) {
          this.chartColor = 'success';
        } else if (percentage >= 80) {
          this.chartColor = 'primary';
        } else if (percentage >= 70) {
          this.chartColor = 'info';
        } else if (percentage >= 60) {
          this.chartColor = 'warning';
        } else {
          this.chartColor = 'danger';
        }
      }
    });




    this.memberPercentageList?.map((element: any) => {
      if (this.organizationId === element?.organization?.id) {
        return parseInt(element?.impactScore);
      }
    });
    this.sharedService.isLoading.next(false);
  }

  get getCountStartValue() {
    return this.sharedService.getCountStartValue(this.relationshipsCount);
  }

  get getCountEndValue() {
    return this.sharedService.getCountEndValue(this.relationshipsCount);
  }

  updatePointsList(event: any): void {
    this.getAllMembers();
  }

  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  nevigateToPersons(associationData: any): void {
    this.router.navigate([
      '/students/view-student',
      associationData?.person?.id,
    ]);
  }

  getRelationshipsList() {
    this.allUsers = [];
    this.personToPersonService
      .getPersonToPersonRelationshipsList(this.organizationId, 'organization')
      .subscribe({
        next: ({ data }: any) => {
          this.associationList = data?.associationsByDate?.items.filter(
            (element: any) => element
          );
          this.associationList = data?.associationsByDate?.items.filter(
            (element: any) =>
              element &&
              !element?._deleted &&
              !element?.business?._deleted &&
              !element?.person?._deleted &&
              element.person &&
              !element?.organization?._deleted &&
              element?.cityId === this.sharedService?.defaultCityId?.value &&
              ((element.type === 'Person' &&
                element.otherPersonsID &&
                element.otherPerson) ||
                ((element.type === 'Business' ||
                  element.type === 'Organization') &&
                  (!element.otherPersonsID ||
                    element.otherPersonsID === 'null') &&
                  !element?.otherPerson))
          );

          this.associationList?.forEach((element: any) => {
            if (element !== null) {
              this.allUsers.push(element?.person);
              element.personName = element?.person?.name;
              if (element?.type === 'Organization') {
                element.otherRelationName = element?.organization?.name;
              }
            }
          });
          this.filteredAssociationList = this.associationList;

          this.filteredAssociationList.forEach((element: any) => {
            if (element?.person?.imageUrl) {
              Storage.get(element?.person?.imageUrl, {
                level: 'public',
              }).then((result: string) => {
                element.imagePreview = result;
              });
            }
          });

          this.relationshipsCount = this.filteredAssociationList?.length;
        },
        error: (error: any) => {
        },
      });
  }

  getOrganizationDetails() {
    this.sharedService.isLoading.next(true);

    this.organizationsService
      .getOrganizationById(this.organizationId)
      .pipe(
        map((val) => {
          if (val.data.getOrganizations !== null) {
            val.data.getOrganizations.contactPhoneNumber =
              val.data.getOrganizations.contactPhoneNumber.substr(
                val.data.getOrganizations.contactPhoneNumber.length - 10
              );
          }
          return val;
        })
      )
      .subscribe({
        next: ({ data }: any) => {
          if (
            data?.getOrganizations?._deleted ||
            data?.getOrganizations === null
          ) {
            this.location.back();

            this.sharedService.isLoading.next(false);

            this.spinner.hide();

            this.toastr.warning('This organization has been deleted!');

            return;
          }

          this.organizationDetails = data?.getOrganizations;
          this.organizationDocuments = data?.getOrganizations?.memberDocuments;
          this.fileCount = this.organizationDocuments?.length;
          this.categoryLatest['Membership MOUs & Contracts'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Membership MOUs & Contracts')?.sort((a: any, b: any) => (new Date(b.updateDate) as any) - (new Date(a.updateDate) as any))[0];
          this.categoryLatest['Member Organization Logos'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Member Organization Logos')?.sort((a: any, b: any) => (new Date(b.updateDate) as any) - (new Date(a.updateDate) as any))[0];
          this.categoryLatest['Recorded Knowledge Interviews'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Recorded Knowledge Interviews')?.sort((a: any, b: any) => (new Date(b.updateDate) as any) - (new Date(a.updateDate) as any))[0];
          this.categoryLatest['Project & Program Photos'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Project & Program Photos')?.sort((a: any, b: any) => (new Date(b.updateDate) as any) - (new Date(a.updateDate) as any))[0];
          this.organizationsService.homeworkId.next(data?.getOrganizations);

          this.categoryFileSize['Membership MOUs & Contracts'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Membership MOUs & Contracts').reduce((prev: any, curr: any) => prev + (curr.fileSize ?? 0), 0);
          this.categoryFileSize['Member Organization Logos'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Member Organization Logos').reduce((prev: any, curr: any) => prev + (curr.fileSize ?? 0), 0);
          this.categoryFileSize['Recorded Knowledge Interviews'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Recorded Knowledge Interviews').reduce((prev: any, curr: any) => prev + (curr.fileSize ?? 0), 0);
          this.categoryFileSize['Project & Program Photos'] = this.organizationDocuments?.filter((ex: any) => ex.category === 'Project & Program Photos').reduce((prev: any, curr: any) => prev + (curr.fileSize ?? 0), 0);


          // Calculate the percentage based on the TRUE status of the three fields
          const trueFieldsCount =
            (this.organizationDetails.isActive ? 1 : 0) +
            (this.organizationDetails.MOUSigned ? 1 : 0) +
            (this.organizationDetails.boardConfirmed ? 1 : 0);
          const totalFieldsCount: any = 3; // Assuming you have 3 fields in total

          this.statusPercentage =
            totalFieldsCount === 0
              ? 0
              : (trueFieldsCount / totalFieldsCount) * 100;
          this.statusPercentage = Math.trunc(this.statusPercentage);

          if (this.organizationDetails?.contactRole == 'SUPER_ADMIN') {
            this.organizationDetails.contactRole = 'Super Admin';
          } else if (this.organizationDetails?.contactRole == 'STAFF_MEMBER') {
            this.organizationDetails.contactRole = 'Staff Member';
          } else if (this.organizationDetails?.contactRole == 'MEMBER') {
            this.organizationDetails.contactRole = 'Member';
          } else if (this.organizationDetails?.contactRole == 'SUBSCRIBER') {
            this.organizationDetails.contactRole = 'Subscriber';
          }

          if (this.organizationDetails.imageUrl) {
            Storage.get(this.organizationDetails.imageUrl, {
              level: 'public',
            }).then((result: string) => {
              this.organizationDetails.imagePreview = result;

              setTimeout(() => {
                this.sharedService.isLoading.next(false);
                this.spinner.hide();
              }, 2000);
            });
          } else {
            setTimeout(() => {
              this.sharedService.isLoading.next(false);
              this.spinner.hide();
            }, 2000);
          }
        },
        error: (error: any) => {
          this.sharedService.isLoading.next(false);

          this.spinner.hide();
          this.location.back();
        },
      });
  }

  openDeleteConfirmationModal(
    content: any,
    id: string,
    organizationName: string
  ) {
    this.organizationId = id;
    this.organizationName = organizationName;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  closeDeleteConfirmationModal() {
    this.modalService.dismissAll();
  }

  setCurrentTab(value: string) {
    this.currentTab = value;
    this.folderList = this.defaultfolderList;
    this.isFolder = true;
  }

  deleteOrganization() {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    const data = this.cleanOrganizationData(this.organizationDetails);
    const events = this.getIdVersionList(data?.OrganizationsEvents?.items);
    const tasks = this.getIdVersionList(data?.tasks?.items);

    this.organizationsService.updateOrganization(data).pipe(
      switchMap(({ data }: any) => {
        const version = data.updateOrganizations._version;
        return this.organizationsService.deleteOrganization(this.organizationId, version);
      }),
      switchMap(({ data }: any) => {
        this.toastr.success('Successfully deleted member!');
        this.sharedService.deleteRecords(this.organizationId, 'Organizations').subscribe();
        this.sharedService.generateLog({
          type: 'DELETED',
          moduleId: this.organizationId,
          moduleName: data.deleteOrganizations.name,
          moduleType: 'member',
          requestStatus: 'SYSTEM_APPROVED',
          activityType: 'MEMBERSHIP',
          cityId: this.sharedService.defaultCityId.value,
        }).subscribe();

        this.handleMembershipDeletion();

        if (this.organizationDetails.imageUrl) {
          this.deleteFile(this.organizationDetails.imageUrl);
        }

        this.batchDeleteRelatedData(events, tasks);
        return of(null);
      })
    ).subscribe(() => {
      this.router.navigate(['/members']);
      this.closeDeleteConfirmationModal();
      this.sharedService.isLoading.next(false);
      this.spinner.hide();
    });
  }

  private cleanOrganizationData(data: any): any {
    const cleaned = { ...data };
    const deleteFields = [
      'user', 'organizationUser', 'OrganizationsEvents', 'tasks',
      'updatedAt', '_lastChangedAt', 'imagePreview', '_deleted',
      'MOUSigned', 'boardConfirmed', 'cityData', 'firstNameAfterSplit',
      'lastNameAfterSplit', 'membership'];
    deleteFields.forEach(field => delete cleaned[field]);
    data.isDeleted = 'true';
    return cleaned;
  }

  private getIdVersionList(items: any[]): any[] {
    return items?.filter(item => !item._deleted).map(item => ({
      id: item.id,
      _version: item._version,
    })) ?? [];
  }

  private handleMembershipDeletion() {
    this.sharedService.getMember(this.organizationId, 'organization').subscribe(res => {
      const membership = res?.data?.membershipByDate?.items[0];
      if (!membership) return;

      this.sharedService.deleteMembership(membership.id, membership._version).subscribe(({ data }) => {
        const updatedMembership = {
          ...membership,
          isDeleted: 'true',
          _version: data?.deleteMembership?._version,
        };

        ['updatedAt', '_deleted', '_lastChangedAt', 'person', 'organization'].forEach(f => delete updatedMembership[f]);
        this.sharedService.updateMembership(updatedMembership);
      });
    });
  }

  private batchDeleteRelatedData(events: any[], tasks: any[]) {
    const deletes: Observable<any>[] = [];

    if (events.length) {
      deletes.push(this.sharedService.batchDelete(events, 'deleteEventsOrganizations'));
    }
    if (tasks.length) {
      deletes.push(this.sharedService.batchDelete(tasks, 'deleteTaskOrganizations'));
      deletes.push(this.sharedService.batchDelete(tasks, 'deletePersonsOrganizations'));
    }

    if (deletes.length) {
      forkJoin([...deletes]).subscribe();
    }
  }


  deleteFile(key: string) {
    Storage.remove(key, {
      level: 'public',
    }).catch((err) => { });
  }

  getOrganizationRelationDetails() {
    this.eventsService.getEventsList(this.organizationId).subscribe({
      next: ({ data }: any) => {
        if (!data || !data.listEventsOrganizations) {
          return;
        }
        let eventList = data.listEventsOrganizations.items.filter(
          (element: any) => element && !element.events._deleted
        );

        eventList.forEach((element: any) => {
          element.events?.EventsOrganizations?.items
            ?.filter((element: any) => element && !element._deleted)
            .forEach((subElement: any) => {
              this.organizationsEventsIdVersion.push(subElement);
            });
        });
      },
    });

    this.tasksService.getTaskList(this.organizationId).subscribe(({ data }) => {
      let tasksList = data?.listTaskOrganizations?.items?.filter(
        (element: any) => element && !element.task._deleted
      );

      let taskOrganizations = tasksList?.filter(
        (element: any) =>
          element.organizationsID === this.organizationId &&
          element?.organizations &&
          !element?.organizations?._deleted
      );

      taskOrganizations?.forEach((element: any) => {
        this.tasksIdVersion?.push(element);
      });
    });
  }

  deleteEventOrganization() {
    this.eventsService.getEventsList(this.organizationId).subscribe({
      next: ({ data }: any) => {
        let eventList = data?.listEventsOrganizations?.items?.filter(
          (element: any) => element && !element.events._deleted
        );

        eventList.forEach((element: any) => {
          element.events?.EventsOrganizations?.items
            ?.filter((element: any) => element && !element._deleted)
            .forEach((subElement: any) => {
              this.eventsService
                .deleteRelationship(subElement?.id, subElement?._version)
                .subscribe({});
            });
        });
      },
    });
  }

  deleteTaskOrganization() {
    this.tasksService.getTaskList(this.organizationId).subscribe(({ data }) => {
      let tasksList = data?.listTaskOrganizations?.items.filter(
        (element: any) => element && !element.task._deleted
      );

      let taskOrganizations = tasksList.filter(
        (element: any) =>
          element.organizationsID === this.organizationId &&
          element?.organizations &&
          !element?.organizations?._deleted
      );

      taskOrganizations.forEach((element: any) => {
        this.tasksService
          .deleteRelationship(element?.id, element?._version)
          .subscribe({});
      });
    });
  }

  fetchDocuments(category: any) {
    if (this.organizationDocuments) {
      this.isFolder = false;
      this.folderList = this.organizationDocuments.filter((folder: any) => folder.category === category)
      this.fileCount = this.folderList.length;
      this.currentSelectedCategory = category;
    } else {
      this.isFolder = true;
      this.fileCount = this.organizationDocuments?.length ?? 0;
      this.toastr.warning('No files in this category!');
      this.currentSelectedCategory = ''
    }
  }

  downloadBlob(blob: any, filename: any) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename ?? 'download';
    const clickHandler = () => {
      setTimeout(() => {
        URL.revokeObjectURL(url);
        a.removeEventListener('click', clickHandler);
      }, 150);
    };
    a.addEventListener('click', clickHandler, false);
    a.click();
    return a;
  }

  async downloadFile(key: any) {
    try {
      const result: any = await Storage.get(key, { download: true });
      this.downloadBlob(result.Body, key.split('/')[2].split('.')[0]);
    } catch (error) {
      console.error(error)
      this.toastr.error('Error while fetching this document.');
    }
  }
  backToFolderList() {
    this.isFolder = true;
    this.folderList = this.defaultfolderList;
    this.fileCount = this.organizationDocuments.length;
    this.currentSelectedCategory = '';
  }

  public returnFileSize(fileSize: any) {
    //fileSize is in bytes

    let sizeString = '';
    if (fileSize !== 0) {
      sizeString = (fileSize / 1000000).toFixed(2).toString() + ' mb';
      return sizeString;
    }

    if (fileSize === 0) {
      sizeString = '-'
      return sizeString;
    }
  }

  public onChange(selectedFiles: any, content: any, inModal: any) {
    this.handleFiles(selectedFiles?.target?.files, content, inModal);
    if (!this.categoryModal) {
      this.documentCategory.enable();
      this.documentCategory.reset();
    }
  }

  private handleFiles(files: FileList | null, content: any, inModal: boolean) {
    this.currentCategoryDocuments = this.currentCategoryDocuments ?? [];
    if (files) {
      if (this.showCategoryDocuments) {
        for (const file of Array.from(files)) {
          this.currentCategoryDocuments.push({ category: this.documentCategory.value, name: 'member-documents/' + this.organizationId + '/' + file.name, updateDate: new Date().toISOString(), file });
        }
      } else {
        for (const file of Array.from(files)) {
          this.files.push(file);
        }
      }
      if (!inModal) {
        this.modalService.open(content, {
          size: 'md',
          backdrop: 'static',
          keyboard: false
        });
      }
    }
  }

  public fileDrop(event: any, content: any, inModal: boolean) {
    event.preventDefault();
    if (!this.categoryModal) {
      this.documentCategory.enable();
    }
    event.stopPropagation();
    this.handleFiles(event?.dataTransfer?.files, content, inModal);
  }
  public fileDragOver(event: any) {
    event.preventDefault();
    if (!this.categoryModal) {
      this.documentCategory.enable();
    }
    event.stopPropagation();
  }
  public removeFile(index: any, source: any) {

    if (this.showCategoryDocuments) {
      source.splice(index, 1);
      this.removeDocumentList.push(source[index]?.name);
    } else {
      source.splice(index, 1);
    }
    return source;
  }
  public changeCategory(event: any) {
    this.documentCategory.setValue(event.target.value);
    this.categoryModal = false;
  }
  public async closeModal() {
    this.modalService.dismissAll();
    this.files = [];
    this.currentCategoryDocuments = [];
    this.categoryModal = false;
    this.documentCategory.reset();
    this.showCategoryDocuments = false;
  }
  public async uploadFiles() {
    if (!this.documentCategory.valid) {
      this.documentCategory.markAsTouched();
      return;
    }

    let updateDocuments: any[] = [];
    this.otherDocuments = this.documents?.filter((ex: any) => ex.category !== this.documentCategory.value) ?? [];

    if (!this.categoryModal) {
      const oldDocuments = this.documents?.filter((ex: any) => ex.category === this.documentCategory.value) ?? [];
      this.currentCategoryDocuments = [...this.currentCategoryDocuments, ...oldDocuments];
    }

    const filesToUpload = this.showCategoryDocuments ? this.currentCategoryDocuments : this.files;

    if (this.showCategoryDocuments) {
      await this.removeDocumentsFromStorage();
    }

    updateDocuments = await this.uploadFilesToStorage(filesToUpload);

    if (!this.showCategoryDocuments && !this.categoryModal) {
      const oldDocuments = this.documents?.filter((ex: any) => ex.category === this.documentCategory.value) ?? [];
      updateDocuments = [...updateDocuments, ...oldDocuments];
    }

    if (this.showCategoryDocuments) {
      this.currentCategoryDocuments?.forEach((item: any) => delete item.file);
    }

    this.finalizeUpdate(updateDocuments);
  }

  private async removeDocumentsFromStorage(): Promise<void> {
    for (const key of this.removeDocumentList) {
      try {
        await Storage.remove(key, { level: 'public' });
      } catch (error) {
        this.toastr.error('Error removing file' + error);
      }
    }
  }

  private async uploadFilesToStorage(files: any[]): Promise<any[]> {
    const uploaded: any[] = [];

    for (const file of files) {
      try {
        const fileObj = file?.file ?? file;
        const fileName = file?.name?.split?.('/')?.slice?.(-1)?.[0] ?? file?.name;

        const result = await Storage.put(
          `member-documents/${this.organizationId}/${fileName}`,
          fileObj,
          {
            level: 'public',
            contentType: fileObj?.type,
          }
        );

        uploaded.push(this.buildUpdateDocumentObject(file, result));

      } catch (error) {
        this.toastr.error('Error uploading file' + error);
      }
    }

    return uploaded;
  }

  private buildUpdateDocumentObject(file: any, result: any): any {
    const fileSize = file?.fileSize ?? file?.file?.size ?? file?.size ?? 0;
    return {
      name: result?.key,
      category: this.documentCategory.value,
      updateDate: new Date().toISOString(),
      fileSize,
    };
  }

  private finalizeUpdate(updateDocuments: any[]): void {
    this.organizationsService.updateOrganization({
      id: this.organizationId,
      memberDocuments: [...updateDocuments, ...this.otherDocuments],
      _version: this.version,
    }).subscribe({
      next: () => {
        this.files = [];
        this.documentCategory.reset();
        location.reload();
      }
    });

    this.modalService.dismissAll();
    this.showCategoryDocuments = false;
  }

  getAllTokens() {
    this.sharedService.getCityFundTransactions(this.sharedService.defaultCityId.value).subscribe({
      next: ((response: any) => {
        this.cityFundData = response?.data?.cityFundTransactionsByDate?.items.map((fund: any) => {
          if (!(fund.description)) {
            if (fund?.type === 'subscription') {
              fund.name = this.userList[fund?.name]?.name;
              fund['description'] = `${fund?.name ?? ''} has subscribed to ${fund?.productName}.`;
            }
            else if (fund?.type === 'tokenTransfer') {
              if (fund?.memberData != null && Object.keys(fund?.memberData).length !== 0) {
                fund['description'] = `${fund?.memberData?.name} has transfered tokens to ${fund?.productName}.`;
              } else if (fund?.userData != null && Object.keys(fund?.userData).length !== 0) {
                fund['description'] = `${fund?.userData?.name} has transfered tokens to ${fund?.productName}.`;
              }
            }
            else if (fund?.amountStatus === 'CREDITED') {
              fund['description'] = `${fund?.name} has donated $${fund?.amount}.`;
            } else {
              fund['description'] = `${fund?.name} has recieved $${fund?.amount} from ${fund?.cityData?.name}.`
            }
          }
          return fund;
        });
        this.cityFundData.map((fund: any) => {
          if (fund.memberId === this.organizationId) {
            if (fund.amountStatus === "CREDITED") {
              this.totalFunds -= fund.amount
            }
            if (fund.amountStatus === "DEBITED") {
              this.totalFunds += fund.amount
            }
          }
        })
      })
    })
  }
}
