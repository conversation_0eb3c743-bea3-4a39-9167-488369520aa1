import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Storage } from 'aws-amplify';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import * as XLSX from 'xlsx';
import { forkJoin, map } from 'rxjs';

import { FilterPipe } from './pipes/filter-pipe';
import { SharedService } from '../../shared/services/shared.service';
import { OrganizationsService } from './services/organizations.service';
import { ActivitiesService } from '../activities/services/activities.service';
import { PersonsService } from '../persons/services/persons.service';
import { memberImportValidationRules } from 'src/app/shared/models/import-validation-rules.model';
import { ModuleName } from 'src/app/shared/enums/module-name.enum';

@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  styleUrls: ['./organizations.component.scss'],
})
export class OrganizationsComponent implements OnInit {
  filters: any;
  organizationDetails: any;
  s3ObjectsList: any[] = [];
  organizationsList: any[] = [];

  _version: number;
  organizationsCount: number = 0;
  activitiesList: any[] = [];
  fileKey: string;
  exportType: string = '';
  showOptions: string = '';
  organizationId: string = '';
  organizationName: string = '';
  currentTab: string = 'Table Panel';
  sortBy: string = 'Recently Updated';
  firstName: string = '';
  lastName: string = '';
  file: File | null = null;
  validationResults: string[] = [];

  roleMap: { [key: string]: string } = {
    SCHOOL_OWNER: 'School Owner',
    SCHOOL_EMPLOYEE: 'School Employee',
    CUSTOMER: 'Customer',
    PRESIDENT: 'President',
    EXECUTIVE_DIRECTOR: 'Executive Director',
    BOARD_CHAIR: 'Board Chair',
    BOARD_MEMBER: 'Board Member',
    STAFF: 'Staff',
    VOLUNTEER: 'Volunteer',
    SUPER_ADMIN: 'Super Admin',
    STAFF_MEMBER: 'Staff Member',
    MEMBER: 'Member',
    SUBSCRIBER: 'Subscriber',
  };

  constructor(
    private readonly router: Router,
    private readonly toastr: ToastrService,
    private readonly filterPipe: FilterPipe,
    private readonly modalService: NgbModal,
    private readonly spinner: NgxSpinnerService,
    public sharedService: SharedService,
    private readonly organizationsService: OrganizationsService,
    public readonly activitiesService: ActivitiesService,
    public personsService: PersonsService
  ) { }

  ngOnInit() {
    this.getOrganizationsList();
    this.getActivitiesList();
    if (this.organizationsService.filters) {
      this.filters = this.organizationsService.filters;
    }
  }

  getActivitiesList() {
    this.sharedService.isLoading.next(true);

    forkJoin([
      this.activitiesService.getActivitiesList(),
      this.sharedService.getCognitoUsersList(),
    ]).subscribe({
      next: (response: any) => {
        this.sharedService.isLoading.next(false);

        this.spinner.hide();

        this.activitiesList = response[0].data.activitiesByDate.items.filter(
          (element: any) => !element._deleted
        );

        this.activitiesList = this.activitiesList?.map((activityObj: any) => ({
          ...activityObj,
          createdUserName: response[1]?.data?.users?.find(
            (user: { userId: string; name: string }) =>
              user?.userId === activityObj?.createdUserId
          )?.name || activityObj.createdUserName || ''
        }));
      },
      error: (error: any) => { },
    });
  }

  setFilters(id: string) {
    this.organizationsService.filters = this.filters;
    this.organizationsService.currentPage = this.currentPage;

    this.router.navigate([
      '/members/view-member',
      this.sharedService.getEncryptedId(id),
    ]);
  }

  openDeleteConfirmationModal(content: any, organizationDetails: any) {
    this.organizationId = organizationDetails.id;
    this.organizationName = organizationDetails.name;
    this._version = organizationDetails._version;

    this.organizationDetails = organizationDetails;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  closeDeleteConfirmationModal() {
    this.modalService.dismissAll();
  }

  setCurrentTab(tab: string) {
    this.currentTab = tab;
  }

  getFieldValue(record: any, field: string, defaultValue: string = ""): string {
    return field in record ? record[field] : defaultValue;
  }

  /**
 * * On File Select
 * ? This function is used for uploading user selected file for uploading process.
 * ? Scenario check while import organization(member)
 * ? 1. If same organizations and different stakeholder added in the sheet then it will add the 2 organizations and 2 stakeholders
 * ? 2. If same organizations and same stakeholder added in the sheet then it will add the 1 organization and 1 stakeholder
 * ? 3. If different organizations and different stakeholder added in the sheet then it will add the 2 organizations and 2 stakeholders
 * ? 4. If different organizations and same stakeholder added in the sheet then it will add the 1 organization and 1 stakeholder
 * @param ev This parameter holds the file data of the selected file.
 * 
 */
  onFileSelect(ev: any): void {
    this.spinner.show();
    this.file = ev.target.files[0];
    if (!this.file) {
      this.validationResults.push('Please upload an Excel file first.');
      return;
    }

    const reader = new FileReader();

    reader.onload = (e: any) => {
      try {
        const arrayBuffer = e.target.result;
        const wb = XLSX.read(arrayBuffer, { type: 'array' });  // Change from 'binary' to 'array'
        const ws = wb.Sheets[wb.SheetNames[0]];
        const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
        const payload = wb.SheetNames.reduce((initial: any, name: any) => {
          const sheet = wb.Sheets[name];
          initial['items'] = XLSX.utils.sheet_to_json(sheet);
          return initial;
        }, {});

        let ref: any = {
          "name": "name",
          "type": "type",
          "shortDescription": "shortDescription",
          "longDescription": "longDescription",
          "organizationStructure": "organizationStructure",
          "organizationFunction": "organizationFunction",
          "teamSize": "teamSize",
          "estimatedAnnualBudget": "estimatedAnnualBudget",
          "organizationImpact": "organizationImpact",
          "status": "status",
          "contactFirstName": "contactFirstName",
          "contactLastName": "contactLastName",
          "contactEmail": "contactEmail",
          "contactPhoneNumber": "contactPhoneNumber",
          "isActive": "isActive",
          "cityId": "cityId",
          "chapterName": "chapterName",
          "Name": "name",
          "Type": "type",
          "Short Description": "shortDescription",
          "Long Description": "longDescription",
          "Member Structure": "organizationStructure",
          "Member Function": "organizationFunction",
          "Estimated Annual Budget": "estimatedAnnualBudget",
          "Member Impact": "organizationImpact",
          "Team Size": "teamSize",
          "Status": "status",
          "Contact First Name": "contactFirstName",
          "Contact Last Name": "contactLastName",
          "Contact Email": "contactEmail",
          "Contact Phone Number": "contactPhoneNumber",
          "Is Active": "isActive",
          "Village": "cityId",
          "Chapter Name": "chapterName"
        }

        // Change heading row to DB keys in data.
        let headingRow: any = data[0];
        headingRow = headingRow.map((title: any) => { return ref[title] });
        data[0] = headingRow;

        // Change keys in payload for API call.
        const jsonData = {
          items: payload.items.map((record: any) => {
            return {
              name: this.getFieldValue(record, "Name", record["name"]),
              type: this.getFieldValue(record, "Type", record["type"]),
              shortDescription: this.getFieldValue(record, "Short Description", record["shortDescription"]),
              longDescription: this.getFieldValue(record, "Long Description", record["longDescription"]) ?? "",
              organizationStructure: this.getFieldValue(record, "Member Structure", record["organizationStructure"]) ?? "",
              organizationFunction: this.getFieldValue(record, "Member Function", record["organizationFunction"]) ?? "",
              teamSize: this.getFieldValue(record, "Team Size", record["teamSize"]),
              estimatedAnnualBudget: this.getFieldValue(record, "Estimated Annual Budget", record["estimatedAnnualBudget"]),
              organizationImpact: this.getFieldValue(record, "Member Impact", record["organizationImpact"]) ?? "",
              status: this.getFieldValue(record, "Status", record["status"]),
              contactFirstName: this.getFieldValue(record, "Contact First Name", record["contactFirstName"]),
              contactLastName: this.getFieldValue(record, "Contact Last Name", record["contactLastName"]),
              contactEmail: this.getFieldValue(record, "Contact Email", record["contactEmail"]),
              contactPhoneNumber: this.getFieldValue(record, "Contact Phone Number", record["contactPhoneNumber"]),
              isActive: this.getFieldValue(record, "Is Active", record["isActive"]),
              cityId: this.getFieldValue(record, "Village", record["cityId"]),
              chapterName: this.getFieldValue(record, "Chapter Name", record["chapterName"]) ?? "",
            };
          })
        };

        jsonData.items.forEach((iterator: any) => {
          if (typeof iterator?.isActive !== 'boolean') {
            if (iterator?.isActive && iterator?.isActive.toLowerCase() === 'true') {
              iterator.isActive = true;
            } else if (iterator?.isActive && iterator?.isActive.toLowerCase() === 'false') {
              iterator.isActive = false;
            } else {
              iterator.isActive = false;
            }
          }
        });

        const result = this.sharedService.importSheetValidations(data, memberImportValidationRules);
        this.validationResults = result.errors;
        const { items, hasDuplicates } = this.sharedService.removeDuplicates(jsonData, ModuleName.ORGANIZATIONS);
        if (this.validationResults.length === 0) {
          this.sharedService?.uploadMembersFile({ items }).subscribe({
            next: (res: any) => {
              this.getOrganizationsList();
              this.sharedService.importMetaData.next({
                module: 'member',
                count: res?.data?.importOrganizations?.data?.fail?.length,
                data: res?.data?.importOrganizations?.data?.fail.length > 0 ? res?.data?.importOrganizations?.data?.fail : [],
                fromAPI: res?.data?.importOrganizations?.data?.fail.length > 0,
                successCount: res?.data?.importOrganizations?.data?.success
              });
              this.router.navigate(['/import-file'], { queryParams: { hasDuplicates: hasDuplicates } });
            },
            error: () => { },
          });
        } else {
          // Handle validation errors
          this.sharedService.importMetaData.next({
            count: result.errorRowCount,
            module: 'member',
            data: this.validationResults
          });
          this.router.navigate(['/import-file'], { queryParams: { hasDuplicates: hasDuplicates } });
          this.file = null;
          this.spinner.hide();
        }
      } catch (error: any) {
        this.validationResults.push('Error reading the file: ' + error.message);
        this.file = null;
      }
    };

    reader.onerror = (error: any) => {
      this.validationResults.push('Error reading the file: ' + error.message);
      this.file = null;
    };

    reader.readAsArrayBuffer(this.file);  // Use readAsArrayBuffer instead
  }

  getOrganizationsList() {
    this.organizationsService
      .getOrganizationsList()
      .pipe(map(val => {
        let dataVal = val.data?.organizationsByDate?.items?.filter((org: any) => org !== null) ?? [];
        if (dataVal && dataVal.length > 0) {
          return dataVal.map((userListData: any) => {
            userListData.contactPhoneNumber = userListData.contactPhoneNumber?.substr(
              userListData.contactPhoneNumber.length - 10
            );
            return userListData;
          });
        }
        return dataVal;
      }))
      .subscribe((data: any) => {
        this.organizationsList = data;

        for (const user of this.organizationsList) {
          const fullName = user.contactFullName;
          const nameParts = fullName.split(" ");
          if (nameParts.length >= 2) {
            user.firstNameAfterSplit = nameParts[0];
            user.lastNameAfterSplit = fullName.substring(user.firstNameAfterSplit.length + 1);
          }
        }

        this.organizationsList.forEach((element: any) => {
          if (element?.contactRole == 'SUPER_ADMIN') {
            element.contactRole = 'Super Admin';
          } else if (element?.contactRole == 'STAFF_MEMBER') {
            element.contactRole = 'Staff Member';
          } else if (element?.contactRole == 'MEMBER') {
            element.contactRole = 'Member';
          } else if (element?.contactRole == 'SUBSCRIBER') {
            element.contactRole = 'Subscriber';
          }

          if (element?.imageUrl) {
            Storage.get(element.imageUrl, {
              level: 'public',
            }).then((result: string) => {
              element.imagePreview = result;
            });
          }
        });

        this.organizationsCount = this.organizationsList.length;

        this.applyFilter(
          this.filters,
          this.organizationsService.currentPage || 1
        );
      });
    this.spinner.hide();
  }

  get currentPage() {
    return this.sharedService.currentPage.value;
  }

  get getCountStartValue() {
    return this.sharedService.getCountStartValue(this.organizationsCount);
  }

  get getCountEndValue() {
    return this.sharedService.getCountEndValue(this.organizationsCount);
  }

  applyFilter(value: any, currentPage?: number) {
    this.filters = value;
    this.organizationsCount = this.filterPipe.transform(
      this.organizationsList,
      this.filters,
      this.sortBy
    ).length;

    this.sharedService.currentPage.next(currentPage || 1);
  }

  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  deleteOrganization() {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    const cleanedData = this.prepareOrganizationData();
    const organizationsEventsIdVersion = this.extractValidItems(cleanedData.OrganizationsEvents?.items);
    const tasksIdVersion = this.extractValidItems(cleanedData.tasks?.items);

    this.organizationsService.updateOrganization(cleanedData).subscribe({
      next: ({ data }: any) => {
        const updatedVersion = data.updateOrganizations?._version;

        this.organizationsService.deleteOrganization(this.organizationId, updatedVersion).subscribe({
          next: ({ data }: any) => {
            this.handlePostDelete(
              data,
              organizationsEventsIdVersion,
              tasksIdVersion
            );
          },
          error: (error: any) => {
            this.handleError(error);
          },
        });
      },
      error: (error: any) => {
        this.handleError(error);
      },
    });
  }

  private prepareOrganizationData(): any {
    const data = { ...this.organizationDetails };
    const deleteFields = [
      'user', 'organizationUser', 'OrganizationsEvents', 'tasks',
      'updatedAt', '_lastChangedAt', 'imagePreview', '_deleted',
      'MOUSigned', 'boardConfirmed', 'cityData', 'firstNameAfterSplit',
      'lastNameAfterSplit', 'membership'
    ];

    deleteFields.forEach(field => delete data[field]);
    data.isDeleted = 'true';
    return data;
  }

  private extractValidItems(items: any[] = []): any[] {
    return items
      .filter(item => !item._deleted)
      .map(item => ({ id: item.id, _version: item._version }));
  }

  private handlePostDelete(data: any, events: any[], tasks: any[]) {
    this.toastr.success('Successfully deleted member!');
    this.sharedService.deleteRecords(this.organizationId, 'Organizations').subscribe();

    this.cleanupMembership();
    this.logDeletion(data);
    this.cleanupFilesAndRelations(events, tasks);
    this.updateUI();
  }

  private cleanupMembership() {
    this.sharedService.getMember(this.organizationId, 'organization').subscribe((res: any) => {
      const membership = res?.data?.membershipByDate?.items[0];

      if (membership) {
        this.sharedService.deleteMembership(membership.id, membership._version).subscribe(({ data }) => {
          const editMembership = {
            ...membership,
            isDeleted: 'true',
            _version: data?.deleteMembership?._version
          };

          delete editMembership.updatedAt;
          delete editMembership._deleted;
          delete editMembership._lastChangedAt;
          delete editMembership.person;
          delete editMembership.organization;

          this.personsService.editMembership(editMembership).subscribe(() => {
            this.sharedService.isLoading.next(false);
            this.spinner.hide();
          });
        });
      }
    });
  }

  private logDeletion(data: any) {
    this.sharedService.generateLog({
      type: 'DELETED',
      moduleId: this.organizationId,
      moduleName: data.deleteOrganizations.name,
      moduleType: 'member',
      requestStatus: 'SYSTEM_APPROVED',
      activityType: 'MEMBERSHIP',
      cityId: this.sharedService.defaultCityId.value,
    }).subscribe();
  }

  private cleanupFilesAndRelations(events: any[], tasks: any[]) {
    if (this.organizationDetails.imageUrl) {
      this.deleteFile(this.organizationDetails.imageUrl);
    }

    if (events.length) {
      this.sharedService.batchDelete(events, 'deleteEventsOrganizations');
    }

    if (tasks.length) {
      this.sharedService.batchDelete(tasks, 'deleteTaskOrganizations');
    }
  }

  private updateUI() {
    this.organizationsList = this.organizationsList.filter(org => org?.id !== this.organizationId);

    const filtered = this.filterPipe.transform(this.organizationsList, this.filters, this.sortBy);
    this.organizationsCount = filtered.length;

    this.sharedService.currentPage.next(1);
    this.closeDeleteConfirmationModal();
    this.sharedService.isLoading.next(false);
    this.spinner.hide();
  }

  private handleError(error: any) {
    this.sharedService.isLoading.next(false);
    this.spinner.hide();
    console.error('Deletion failed:', error);
  }

  deleteFile(key: string) {
    Storage.remove(key, {
      level: 'public',
    }).catch((err) => {
      this.spinner.hide();
    });
  }

  exportOrganizations(event: any) {
    if (event.value === 'excel') {
      this.organizationsService.exportExcel();
    }

    if (event.value === 'pdf') {
      this.organizationsService.exportPDF(
        this.filterPipe.transform(
          this.organizationsList,
          this.filters,
          this.sortBy
        )
      );
    }

    event.value = '';
  }

  downloadBlob(blob: any, filename: any) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename ?? 'download';
    const clickHandler = () => {
      setTimeout(() => {
        URL.revokeObjectURL(url);
        a.removeEventListener('click', clickHandler);
      }, 150);
    };
    a.addEventListener('click', clickHandler, false);
    a.click();
    return a;
  }

  async downloadFile() {
    try {
      let key = 'member-organization/Import-Member.xlsx';
      const result: any = await Storage.get(key, { download: true });
      this.downloadBlob(result.Body, "Import-Member");
    } catch (error) {
      console.error(error)
      this.toastr.error('Error while fetching this document.');
    }
  }

  getRoleDisplayName(role: string): string {
    return this.roleMap[role] || role;
  }
}
