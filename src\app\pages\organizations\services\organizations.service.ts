import { CurrencyPipe, TitleCasePipe } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { MaskPipe } from 'ngx-mask';
import { createPdf } from 'pdfmake/build/pdfmake.min';
import pdfMake from 'pdfmake/build/vfs_fonts';
import { TDocumentDefinitions } from 'pdfmake/interfaces';
import * as XLSX from 'xlsx';
import { BaseApiService } from 'src/app/shared/services/base-api.service';
(pdfMake as any).vfs = pdfMake.vfs;

import {
  ADD_ORGANIZATIONS,
  DELETE_ORGANIZATIONS,
  GET_ORGANIZATIONS,
  GET_ORGANIZATIONS_BY_ID,
  UPDATE_ORGANIZATIONS,
} from '../graphql/organizations-graphql.queries';
import { SharedService } from 'src/app/shared/services/shared.service';

@Injectable({
  providedIn: 'root',
})
export class OrganizationsService {
  filters: any;

  currentPage: number;
  homeworkId: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  constructor(
    readonly router: Router,
    @Inject(BaseApiService) private readonly baseApiService: BaseApiService,
    private readonly maskPipe: MaskPipe,
    private readonly currencyPipe: CurrencyPipe,
    private readonly titleCasePipe: TitleCasePipe,
    private readonly sharedService: SharedService
  ) {
    router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        if (
          !(
            router.url.includes('view-organization') ||
            router.url === '/organizations'
          )
        ) {
          this.filters = null;
          this.currentPage = 1;
        }
      }
    });
  }

  addOrganization(data: any): Observable<any> {
    return this.baseApiService.mutateData(ADD_ORGANIZATIONS, data);
  }

  getOrganizationsList(limit?: number): Observable<any> {
    const filter = { cityId: { eq: this.sharedService.defaultCityId.value } };
    return this.baseApiService.queryWithCityFilter(GET_ORGANIZATIONS, {
      filter,
      limit: limit ?? undefined
    });
  }

  getOrganizationById(organizationId: string): Observable<any> {
    return this.baseApiService.queryById(GET_ORGANIZATIONS_BY_ID, organizationId);
  }

  updateOrganization(input: any): Observable<any> {
    return this.baseApiService.mutateData(UPDATE_ORGANIZATIONS, input);
  }

  deleteOrganization(organizationId: string, version: number): Observable<any> {
    return this.baseApiService.mutateData(DELETE_ORGANIZATIONS, {
      id: organizationId,
      _version: version
    });
  }

  exportExcel() {
    const date: string = new Date()
      .toLocaleString()
      .replace(/\//g, '-')
      .replace(/:/g, '.');

    let element = document.getElementById('organizations_table');
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();

    ws['A1'].s = {
      fill: {
        patternType: 'solid',
        fgColor: { rgb: '0000ff' },
        bgColor: { rgb: '0000ff' },
      },
    };

    ws['!ref'] = ws['!ref']?.replace('G', 'F');
    ws['!cols'] = [
      { wch: 25 }, //Name
      { wch: 15 }, //Type
      { wch: 50 }, //Short Desc.
      { wch: 80 }, //Long Desc.
      { wch: 22 }, //Structure
      { wch: 15 }, //Function
      { wch: 25 }, //Estimated Budget
      { wch: 15 }, //Team Size
      { wch: 20 }, //Chapter
      { wch: 10 }, //Status
      { wch: 20 }, //First Name
      { wch: 20 }, //Last Name
      { wch: 25 }, //Email
      { wch: 25 }, //Phone Number
      { wch: 20 }, //Role
      { wch: 20 }, //Is Active
      { wch: 25 }, //City Id
    ];

    XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');

    XLSX.writeFile(wb, `My Village Members ${date}.xlsx`);
  }

  exportPDF(organizationsList: any) {
    const date: string = new Date()
      .toLocaleString()
      .replace(/\//g, '-')
      .replace(/:/g, '.');

    const dd: TDocumentDefinitions = {
      pageSize: 'A3',
      pageOrientation: 'landscape',
      pageMargins: [20, 20, 20, 20],
      content: [
        {
          style: 'tableExample',
          table: {
            headerRows: 1,
            body: [
              [
                { text: 'Name', style: 'header' },
                { text: 'Type', style: 'header' },
                {
                  text: 'Short Description',
                  style: 'header',
                },
                {
                  text: 'Long Description',
                  style: 'header',
                },
                { text: 'Structure', style: 'header' },
                { text: 'Function', style: 'header' },
                {
                  text: 'Estimated Budget',
                  style: 'header',
                },
                { text: 'Team Size', style: 'header' },
                { text: 'Chapter', style: 'header' },
                { text: 'Status', style: 'header' },
                {
                  text: 'Contact First Name',
                  style: 'header',
                },
                {
                  text: 'Contact Last Name',
                  style: 'header',
                },
                { text: 'Contact Email', style: 'header' },
                {
                  text: 'Contact Phone Number',
                  style: 'header',
                },
                { text: 'Contact Role', style: 'header' },
                { text: 'Is Active', style: 'header' },
                { text: 'City Id', style: 'header' },
              ],
            ],
          },
          layout: {
            paddingLeft: () => {
              return 5;
            },
            paddingRight: () => {
              return 5;
            },
            paddingTop: () => {
              return 7;
            },
            paddingBottom: () => {
              return 7;
            },
          },
        },
      ],
      styles: {
        header: {
          bold: true,
          fillColor: '#CCCCCC',
        },
      },
    };


    organizationsList.map((element: any, index: number) => {
      let teamSize: string;
      if (element['teamSize'] === 'small') {
        teamSize = '1-10';
      } else if (element['teamSize'] === 'medium') {
        teamSize = '10-50';
      } else {
        teamSize = '50+';
      }

      (<any>dd).content[0].table.body.push([
        this.titleCasePipe.transform(element['name']),
        element['type'] === 'other'
          ? 'Other'
          : 'Registered 501c3 tax-exempt nonprofit',
        element['shortDescription'],
        element['longDescription'],
        this.titleCasePipe.transform(element['organizationStructure']) +
        ' Organization',
        element['organizationFunction'],
        this.currencyPipe.transform(
          element['estimatedAnnualBudget'],
          'USD',
          'symbol',
          '0.0'
        ),
        teamSize,
        this.titleCasePipe.transform(element['chapterName']),
        element['isActive'] ? 'Active' : 'Inactive',
        element?.organizationUser ? this.titleCasePipe.transform(element?.organizationUser['givenName']) : '-',
        element?.organizationUser ? this.titleCasePipe.transform(element?.organizationUser['familyName']) : '-',
        element['contactEmail'],
        this.maskPipe.transform(element['contactPhoneNumber'], '************'),
        element['contactRole'],
        element['isActive'],
        element?.cityData ? element?.cityData['name'] : '-'
      ]);
    });

    createPdf(dd).download(`My Village Members ${date}.pdf`);
  }
}
