import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { environment } from '../../../../environments/environment';
import {
  CREATE_ONRAMP_SESSION,
} from '../graphql/crypto-onramp-graphql.queries';
import { BaseApiService } from 'src/app/shared/services/base-api.service';

declare global {
  interface Window {
    StripeOnramp?: any;
  }
}

@Injectable({
  providedIn: 'root'
})
export class CryptoOnrampService {
  constructor(
    private baseApiService: BaseApiService
  ) { }

  /**
   * Creates a Stripe Crypto Onramp session
   * @param usdcAmount Amount of USDC to purchase
   * @param userWallet User's blockchain wallet address
   * @returns Observable with session information
   */
  createOnrampSession(usdcAmount: string, userWallet: string): Observable<any> {
    // Validate input parameters
    if (!usdcAmount || typeof usdcAmount !== 'string' || usdcAmount.trim() === '') {
      return throwError(() => new Error('USDC amount is required and must be a non-empty string'));
    }
    if (!userWallet || typeof userWallet !== 'string' || userWallet.trim() === '') {
      return throwError(() => new Error('User wallet address is required and must be a non-empty string'));
    }

    return this.baseApiService.customQuery(
      CREATE_ONRAMP_SESSION,
      {
        usdcAmount: usdcAmount.trim(),
        userWallet: userWallet.trim()
      }
    ).pipe(
      map((response: any) => {
        if (response?.createOnrampSession?.statusCode !== 200) {
          throw new Error(response?.createOnrampSession?.message || 'Failed to create onramp session');
        }
        return response.createOnrampSession.data;
      }),
      catchError(error => {
        console.error('Error creating onramp session:', error);
        return throwError(() => new Error(error?.message || 'An unexpected error occurred'));
      })
    );
  }

  /**
   * Fetches the current MVT/USDC exchange rate from the backend
   * @returns Observable with the exchange rate (number)
   */
  getExchangeRate(): Observable<number> {
    const getExchangeRateQuery = `
      query GetExchangeRate {
        getExchangeRate {
          statusCode
          message
          data {
            rate
          }
        }
      }
    `;

    return this.baseApiService.customQuery(
      getExchangeRateQuery,
      {}
    ).pipe(
      map((response: any) => {
        const rate = response?.getExchangeRate?.data?.rate;
        if (!rate || isNaN(Number(rate))) {
          throw new Error('Failed to fetch exchange rate');
        }
        return Number(rate);
      }),
      catchError(error => {
        console.error('Error fetching exchange rate:', error);
        return throwError(() => new Error('Failed to fetch exchange rate'));
      })
    );
  }

  /**
   * Opens the Stripe Crypto Onramp modal with the provided client secret
   * @param clientSecret Client secret from the onramp session
   * @returns Promise that resolves when onramp is complete
   */
  async openOnramp(clientSecret: string): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        if (!window.StripeOnramp) {
          this.loadScripts().then(() => {
            this.initializeOnramp(clientSecret, resolve, reject);
          }).catch(error => {
            reject(new Error('Failed to load Stripe Onramp scripts: ' + error.message));
          });
        } else {
          this.initializeOnramp(clientSecret, resolve, reject);
        }
      } catch (error) {
        console.error('Error opening onramp:', error);
        reject(new Error(error.message));
      }
    });
  }

  /**
   * Load required Stripe scripts dynamically
   */
  private loadScripts(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (!document.querySelector('script[src*="js.stripe.com/v3"]')) {
          const stripeJs = document.createElement('script');
          stripeJs.src = 'https://js.stripe.com/v3/';
          stripeJs.onload = () => {
            if (!document.querySelector('script[src*="crypto-js.stripe.com/crypto-onramp-outer.js"]')) {
              const cryptoOnrampJs = document.createElement('script');
              cryptoOnrampJs.src = 'https://crypto-js.stripe.com/crypto-onramp-outer.js';
              cryptoOnrampJs.onload = () => resolve();
              cryptoOnrampJs.onerror = (e) => reject(new Error('Failed to load Crypto Onramp script'));
              document.head.appendChild(cryptoOnrampJs);
            } else {
              resolve();
            }
          };
          stripeJs.onerror = (e) => reject(new Error('Failed to load Stripe.js'));
          document.head.appendChild(stripeJs);
        } else if (!document.querySelector('script[src*="crypto-js.stripe.com/crypto-onramp-outer.js"]')) {
          const cryptoOnrampJs = document.createElement('script');
          cryptoOnrampJs.src = 'https://crypto-js.stripe.com/crypto-onramp-outer.js';
          cryptoOnrampJs.onload = () => resolve();
          cryptoOnrampJs.onerror = (e) => reject(new Error('Failed to load Crypto Onramp script'));
          document.head.appendChild(cryptoOnrampJs);
        } else {
          resolve();
        }
      } catch (error) {
        reject(new Error(error.message));
      }
    });
  }

  /**
   * Initialize the Onramp UI after scripts are loaded
   */
  private initializeOnramp(clientSecret: string, resolve: Function, reject: Function): void {
    try {
      const stripeOnramp = window.StripeOnramp(environment.stripePublicKey);

      const onrampSession = stripeOnramp.createSession({
        clientSecret: clientSecret
      });

      let container = document.getElementById('onramp-container');
      if (!container) {
        container = document.createElement('div');
        container.id = 'onramp-container';
        container.style.position = 'fixed';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.zIndex = '9999';
        container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        document.body.appendChild(container);
      }

      onrampSession.mount('#onramp-container');

      onrampSession.addEventListener('onramp_session_updated', (event: any) => {

        const { payload: { session } } = event;
        if (session.status === 'fulfillment_complete') {
          if (container && container.parentNode === document.body) document.body.removeChild(container);
          resolve(session);
        }
      });

      onrampSession.addEventListener('onramp_session_failed', (event: any) => {
        console.error('Onramp session failed:', event);

        if (container) {
          document.body.removeChild(container);
        }

        reject(new Error('Onramp session failed'));
      });

      onrampSession.addEventListener('onramp_session_cancelled', (event: any) => {

        if (container) {
          document.body.removeChild(container);
        }

        reject(new Error('Onramp session cancelled by user'));
      });
    } catch (error) {
      console.error('Error initializing onramp:', error);
      reject(error);
    }
  }

  /**
   * Convenience method to create a session and open the onramp in one step
   * @param usdcAmount Amount of USDC to purchase
   * @param userWallet User's wallet address
   * @returns Promise that resolves when onramp is complete
   */
  purchaseUSDC(usdcAmount: string, userWallet: string): Observable<any> {
    return new Observable(observer => {
      this.createOnrampSession(usdcAmount, userWallet).subscribe({
        next: (sessionData) => {
          try {
            const result = this.openOnramp(sessionData.clientSecret);
            observer.next(result);
            observer.complete();
          } catch (error) {
            observer.error(error);
          }
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }
}