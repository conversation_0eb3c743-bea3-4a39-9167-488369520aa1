import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';

import { SharedService } from './shared.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BaseApiService {
  constructor(
    protected apollo: Apollo,
    protected sharedService: SharedService
  ) {}
  
  public getCityFilter(includeCityId: boolean = true) {
    if (!includeCityId) return {};
    return {
      cityId: { eq: this.sharedService.defaultCityId.value }
    };
  }
  
  /**
   * Executes a GraphQL query with automatic city filtering
   * @param query The GraphQL query to execute
   * @param variablesOrFilter Either a variables object or a filter object (for backward compatibility)
   * @param limit Optional limit for the number of records to return
   * @param includeCityId Whether to include the city filter (default: true)
   */
  public queryWithCityFilter(
    query: any, 
    variablesOrFilter: any = {}, 
    limit?: number, 
    includeCityId: boolean = true
  ) {
    // Handle backward compatibility - if second parameter is not an object with a filter property,
    // treat it as a filter object
    const isLegacyCall = !variablesOrFilter || !variablesOrFilter.hasOwnProperty('filter');
    
    let variables = isLegacyCall ? { filter: variablesOrFilter } : { ...variablesOrFilter };
    
    // Add city filter if needed
    if (includeCityId) {
      const cityFilter = this.getCityFilter();
      variables.filter = variables.filter ? { ...variables.filter, ...cityFilter } : cityFilter;
    }
    
    // Add limit if provided
    if (limit !== undefined) {
      variables.limit = limit;
    } else if (variables.limit === undefined) {
      variables.limit = environment.recordsLimit;
    }
    
    // Clean up the filter - remove null/undefined values
    if (variables.filter) {
      variables.filter = Object.fromEntries(
        Object.entries(variables.filter).filter(([_, v]) => v != null)
      );
      
      // If filter is empty after cleanup, set it to null
      if (Object.keys(variables.filter).length === 0) {
        variables.filter = null;
      }
    }
    
    return this.apollo.query({
      query,
      variables
    });
  }
  
  /**
   * Execute a GraphQL mutation with the provided input
   * @param mutation The GraphQL mutation to execute
   * @param variables The variables to pass to the mutation
   * @returns Observable with the Apollo mutation result
   */
  public mutateData(mutation: any, variables: any) {
    console.log('variables: ', variables);
    // If the variables already contain an 'input' property, use them as is
    // Otherwise, wrap the variables in an 'input' object for backward compatibility
    const mutationVariables = variables.input !== undefined ? variables : { input: variables };
    console.log('mutationVariables: ', mutationVariables);
    
    return this.apollo.mutate({
      mutation,
      variables: mutationVariables
    });
  }
  
  public queryById(query: any, id: string) {
    return this.apollo.query({
      query,
      variables: { id }
    });
  }

  /**
   * Execute a custom GraphQL query with the provided variables
   * @param query The GraphQL query to execute
   * @param variables Variables to pass to the query
   * @returns Observable with the Apollo query result
   */
  public customQuery<T = any>(
    query: any, 
    variables?: Record<string, any>
  ) {
    return this.apollo.query<T>({
      query,
      variables,
      fetchPolicy: 'network-only'
    });
  }
}
